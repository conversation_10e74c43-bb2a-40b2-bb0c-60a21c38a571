// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_PRISMA_URL") // uses connection pooling
  directUrl = env("DATABASE_URL_NON_POOLING") // uses a direct connection
}

// Authentification
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

enum UserRole {
  ADMIN
  USER
}

model User {
  id               String    @id @default(cuid())
  name             String?
  email            String?   @unique
  emailVerified    DateTime?
  profilePictureId String?   @unique
  profilePicture   File?     @relation("UserProfilePicture", fields: [profilePictureId], references: [id], onDelete: SetNull)
  image            String?
  accounts         Account[]
  files            File[]    @relation("UserFiles")
  
  // Custom fields
  username                   String?                     @unique
  role                       UserRole                    @default(USER)
  password                   String?
  hasPassword                Boolean                     @default(false)
  resetPasswordToken         ResetPassordToken?
  userEmailVerificationToken UserEmailVerificationToken?
  lastLocale                 String?
  otpSecret                  String                      @default("")
  otpMnemonic                String                      @default("")
  otpVerified                Boolean                     @default(false)
  uploadsInProgress          FileUploading[]

  // User preferences
  savedFolders               String[]                    @default([])
  hasVisitedOnce             Boolean                     @default(false)

  // Relations projets
  projects Project[] @relation("UserProjects")
  chats    Chat[]    @relation("UserChats")

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Tokens
model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  @@unique([identifier, token])
}

model ResetPassordToken {
  identifier String   @unique
  token      String   @unique
  expires    DateTime
  user       User     @relation(fields: [identifier], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())
}

model UserEmailVerificationToken {
  identifier String   @unique
  token      String   @unique
  expires    DateTime
  user       User     @relation(fields: [identifier], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())
}

// Fichiers
model File {
  id        String   @id @default(cuid())
  key       String   @unique
  filetype  String
  bucket    String
  endpoint  String
  name      String
  type      String
  size      Int
  userId    String
  user      User     @relation("UserFiles", fields: [userId], references: [id], onDelete: Cascade)
  
  // Relations
  userProfilePicture User?          @relation("UserProfilePicture")
  fileUploadingId    String?        @unique
  fileUploading      FileUploading? @relation(fields: [fileUploadingId], references: [id], onDelete: SetNull)
  projectDocument    ProjectDocument?

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model FileUploading {
  id       String   @id @default(cuid())
  key      String   @unique
  filetype String
  bucket   String
  endpoint String
  expires  DateTime

  // Relations
  file   File?
  userId String?
  user   User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
model DocumentChunk {
  id                String   @id @default(cuid())
  content           String   @db.Text
  embedding         Unsupported("vector(1536)")?
  pageNumber        Int?
  metadata          Json?
  projectDocumentId String
  projectDocument   ProjectDocument @relation(fields: [projectDocumentId], references: [id], onDelete: Cascade)
  folderId          String?
  folder            Folder?   @relation(fields: [folderId], references: [id], onDelete: Cascade)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  @@index([projectDocumentId])
}

// Projets et documents
model Project {
  id          String   @id @default(cuid())
  name        String
  description String?  @db.Text
  userId      String
  user        User     @relation("UserProjects", fields: [userId], references: [id], onDelete: Cascade)

  // Embeddings
  projectSummary          String? @db.Text
projectSummaryEmbedding Unsupported("vector(1536)")?

  // Relations
  rootFolders          Folder[]
  allProjectDocuments  ProjectDocument[]
  chats               Chat[]

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Folder {
  id        String   @id @default(cuid())
  name      String
  projectId String?
  project   Project? @relation(fields: [projectId], references: [id], onDelete: Cascade) // Rendons la relation optionnelle aussi
  texteextraitdossier String?            @db.Text  // Ajoutez cette ligne

  // Hiérarchie
  parentId String?
  parent   Folder?  @relation("SubFolders", fields: [parentId], references: [id], onDelete: Cascade)
  children Folder[]  @relation("SubFolders")
  documents ProjectDocument[]

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
documentChunks DocumentChunk[]

  @@index([projectId])
  @@index([parentId])
}

enum ProjectDocumentStatus {
  UPLOADED
  PROCESSING_TEXT
  PROCESSING_OCR
  SUMMARIZING_FILE
  INDEXING_CHUNKS
  READY
  ERROR
}

model ProjectDocument {
  id               String   @id @default(cuid())
  originalFileName String
  mimeType         String
  status           ProjectDocumentStatus @default(UPLOADED)
  processingError  String?  @db.Text
  extractedText    String?  @db.Text
  ocrPerformed     Boolean  @default(false)
  documentChunks   DocumentChunk[] // Relation inverse ajoutée

  // Embeddings
  fileSummary          String? @db.Text
fileSummaryEmbedding Unsupported("vector(1536)")?

  // Relations
  fileId      String  @unique
  file        File    @relation(fields: [fileId], references: [id], onDelete: Restrict)
  projectId   String
  project     Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  folderId    String
  folder      Folder  @relation(fields: [folderId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([projectId])
  @@index([folderId])
}


// Chat
enum MessageRole {
  system
  user
  assistant
  data
}

model Chat {
  id         String    @id @default(cuid())
  projectId  String
  project    Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  userId     String
  user       User      @relation("UserChats", fields: [userId], references: [id], onDelete: Cascade)
  title      String?
  messages   Message[]

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([projectId])
  @@index([userId])
}

model Message {
  id          String     @id @default(cuid())
  chatId      String
  chat        Chat       @relation(fields: [chatId], references: [id], onDelete: Cascade)
  role        MessageRole
  content     String     @db.Text
  parts       Json?
  annotations Json?

  // Timestamps
  createdAt DateTime @default(now())

  @@index([chatId])
}