"use client"

import React, { useEffect, useState } from "react"
import { Calendar, FileText, Folder, HardDrive, TrendingUp } from "lucide-react"

import { trpcClient } from "@/lib/trpc/client"
import { logger } from "@buildismart/lib"

type ProjectInfo = {
  id: string
  name: string
  description: string | null
  createdAt: Date
  updatedAt: Date
  totalDocuments: number
  readyDocuments: number
  processingDocuments: number
  errorDocuments: number
  totalFolders: number
  totalSize: number
  documentsByType: Record<string, number>
  recentDocuments: Array<{
    id: string
    name: string
    status: string
    createdAt: Date
  }>
}

type ProjectInfoComponentProps = {
  projectId: string
}

export function ProjectInfoComponent({ projectId }: ProjectInfoComponentProps) {
  const [projectInfo, setProjectInfo] = useState<ProjectInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchProjectInfo = async () => {
      try {
        setLoading(true)
        const info = await trpcClient.folders.getProjectInfo.query({ projectId })
        setProjectInfo(info)
        setError(null)
      } catch (err) {
        logger.error("Error fetching project info:", err)
        setError("Erreur lors du chargement des informations du projet")
      } finally {
        setLoading(false)
      }
    }

    if (projectId) {
      fetchProjectInfo()
    }
  }, [projectId])

  const formatFileSize = (bytes: number): string => {
    const sizes = ['B', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const getStatusColor = (status: string): string => {
    switch (status) {
      case "READY": return "text-green-600"
      case "ERROR": return "text-red-600"
      case "PROCESSING_TEXT":
      case "PROCESSING_OCR":
      case "SUMMARIZING_FILE":
      case "INDEXING_CHUNKS": return "text-blue-600"
      case "UPLOADED": return "text-yellow-600"
      default: return "text-gray-600"
    }
  }

  const getStatusLabel = (status: string): string => {
    switch (status) {
      case "READY": return "Prêt"
      case "ERROR": return "Erreur"
      case "PROCESSING_TEXT": return "Traitement du texte"
      case "PROCESSING_OCR": return "Reconnaissance OCR"
      case "SUMMARIZING_FILE": return "Résumé en cours"
      case "INDEXING_CHUNKS": return "Indexation"
      case "UPLOADED": return "Téléchargé"
      default: return status
    }
  }

  const getDocumentTypeIcon = (type: string) => {
    switch (type) {
      case "application": return "📄"
      case "image": return "🖼️"
      case "text": return "📝"
      case "video": return "🎥"
      case "audio": return "🎵"
      default: return "📁"
    }
  }

  if (loading) {
    return (
      <div className="rounded-lg border border-gray-200 bg-white p-4">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="rounded-lg border border-red-200 bg-red-50 p-4">
        <p className="text-red-600">{error}</p>
      </div>
    )
  }

  if (!projectInfo) {
    return null
  }

  const completionPercentage = projectInfo.totalDocuments > 0
    ? Math.round((projectInfo.readyDocuments / projectInfo.totalDocuments) * 100)
    : 0

  return (
    <div className="rounded-lg border border-gray-200 bg-white p-4 space-y-4">
      {/* Header */}
      <div className="border-b border-gray-100 pb-3">
        <h2 className="text-xl font-bold text-gray-900">{projectInfo.name}</h2>
        {projectInfo.description && (
          <p className="text-sm text-gray-600 mt-1">{projectInfo.description}</p>
        )}
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-blue-50 rounded-lg p-3">
          <div className="flex items-center gap-2">
            <FileText className="size-5 text-blue-600" />
            <div>
              <p className="text-sm font-medium text-blue-900">Documents</p>
              <p className="text-lg font-bold text-blue-600">{projectInfo.totalDocuments}</p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 rounded-lg p-3">
          <div className="flex items-center gap-2">
            <TrendingUp className="size-5 text-green-600" />
            <div>
              <p className="text-sm font-medium text-green-900">Traités</p>
              <p className="text-lg font-bold text-green-600">{projectInfo.readyDocuments}</p>
            </div>
          </div>
        </div>

        <div className="bg-purple-50 rounded-lg p-3">
          <div className="flex items-center gap-2">
            <Folder className="size-5 text-purple-600" />
            <div>
              <p className="text-sm font-medium text-purple-900">Dossiers</p>
              <p className="text-lg font-bold text-purple-600">{projectInfo.totalFolders}</p>
            </div>
          </div>
        </div>

        <div className="bg-orange-50 rounded-lg p-3">
          <div className="flex items-center gap-2">
            <HardDrive className="size-5 text-orange-600" />
            <div>
              <p className="text-sm font-medium text-orange-900">Taille</p>
              <p className="text-lg font-bold text-orange-600">{formatFileSize(projectInfo.totalSize)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      {projectInfo.totalDocuments > 0 && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Progression du traitement</span>
            <span className="font-medium text-gray-900">{completionPercentage}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${completionPercentage}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-xs text-gray-500">
            <span>{projectInfo.readyDocuments} terminés</span>
            {projectInfo.processingDocuments > 0 && (
              <span>{projectInfo.processingDocuments} en cours</span>
            )}
            {projectInfo.errorDocuments > 0 && (
              <span className="text-red-500">{projectInfo.errorDocuments} erreurs</span>
            )}
          </div>
        </div>
      )}

      {/* Document Types */}
      {Object.keys(projectInfo.documentsByType).length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-900">Types de documents</h3>
          <div className="flex flex-wrap gap-2">
            {Object.entries(projectInfo.documentsByType).map(([type, count]) => (
              <div key={type} className="bg-gray-100 rounded-full px-3 py-1 text-xs">
                <span className="mr-1">{getDocumentTypeIcon(type)}</span>
                <span className="capitalize">{type}</span>
                <span className="ml-1 font-medium">({count})</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent Documents */}
      {projectInfo.recentDocuments.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-900">Documents récents</h3>
          <div className="space-y-1">
            {projectInfo.recentDocuments.map((doc) => (
              <div key={doc.id} className="flex items-center justify-between py-1">
                <span className="text-sm text-gray-700 truncate flex-1">{doc.name}</span>
                <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(doc.status)}`}>
                  {getStatusLabel(doc.status)}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Metadata */}
      <div className="border-t border-gray-100 pt-3 text-xs text-gray-500">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-1">
            <Calendar className="size-3" />
            <span>Créé le {new Date(projectInfo.createdAt).toLocaleDateString()}</span>
          </div>
          {projectInfo.updatedAt !== projectInfo.createdAt && (
            <div className="flex items-center gap-1">
              <span>Modifié le {new Date(projectInfo.updatedAt).toLocaleDateString()}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
