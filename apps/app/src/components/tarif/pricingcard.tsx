"use client"
import React from "react"

type PricingFeature = {
  text: string
  included: boolean
}

type PricingCardProps = {
  title: string
  price: number
  period: string
  features: PricingFeature[]
  onSelect: () => void
  className?: string
  variant?: "standard" | "premium"
  showBadge?: boolean
}

export const PricingCard: React.FC<PricingCardProps> = ({
  title,
  price,
  period,
  features,
  onSelect,
  className = "",
  variant = "standard",
}) => {
  const isPremium = variant === "premium"

  return (
    <div className="relative flex flex-col items-center justify-center">
      <div
        className={`relative w-full max-w-sm rounded-md border-1 p-6 shadow-md transition-all duration-300 sm:max-w-md sm:p-8 md:max-w-lg md:p-10 lg:max-w-[400px] ${
          isPremium ? "bg-primary text-white" : "bg-white text-black"
        } ${className}`}
      >
        {isPremium && (
          <div className="absolute left-1/2 top-0 -translate-x-1/2 -translate-y-1/2">
            <span className="rounded-full bg-blue-200 px-4 py-1 text-sm font-semibold text-blue-800 shadow-md sm:px-5 sm:py-2 sm:text-lg">
              Populaire
            </span>
          </div>
        )}

        <h2 className="mt-8 text-2xl font-bold sm:mt-10">{title}</h2>

        <div className="mt-4 flex items-end gap-3">
          <div className="flex items-end">
            <span className="text-4xl font-bold sm:text-5xl">{price}</span>
            <span className="ml-1 text-xl sm:text-2xl">€/{period}</span>
          </div>
          {isPremium && (
            <div>
              <span className="sm:text-md px-2 py-0.5 text-xs text-secondary shadow-sm sm:px-3 sm:py-1">
                2 mois offerts !
              </span>
            </div>
          )}
        </div>

        <div className={`my-8 h-px w-full sm:my-10 ${isPremium ? "bg-white" : "bg-gray-300"}`} />

        <div className="space-y-4 sm:space-y-6">
          {features.map((feature, index) => (
            <div key={index} className="flex items-center gap-3 sm:gap-4">
              <div
                className={`flex size-7 shrink-0 items-center justify-center rounded-full sm:size-8 ${
                  feature.included
                    ? isPremium
                      ? "bg-white text-primary"
                      : "bg-primary text-white"
                    : isPremium
                      ? "bg-white text-primary"
                      : "bg-primary text-white"
                }`}
              >
                {feature.included ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="3"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <polyline points="20 6 9 17 4 12" />
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="3"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <line x1="18" y1="6" x2="6" y2="18" />
                    <line x1="6" y1="6" x2="18" y2="18" />
                  </svg>
                )}
              </div>
              <p className="text-base sm:text-xl">{feature.text}</p>
            </div>
          ))}
        </div>

        <button
          onClick={onSelect}
          className={`mt-8 h-12 w-full rounded-md text-center text-lg transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-lg sm:mt-12 sm:h-[50px] sm:text-xl ${isPremium ? "bg-white text-black hover:bg-gray-100" : "bg-primary text-white hover:bg-blue-600"} `}
        >
          Choisir ce plan
        </button>
      </div>
    </div>
  )
}
