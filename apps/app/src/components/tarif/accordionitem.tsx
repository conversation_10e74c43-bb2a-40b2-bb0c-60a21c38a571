"use client"

import { useState } from "react"
import { FiPlus, FiX } from "react-icons/fi"

export default function AccordionItem({ title, content }: { title: string; content: string }) {
  const [open, setOpen] = useState(false)

  return (
    <div className="mx-auto w-full max-w-full py-4 sm:max-w-xl sm:py-6 md:max-w-3xl lg:max-w-5xl">
      <button className="flex w-full items-center justify-between gap-4 text-left" onClick={() => setOpen(!open)}>
        <span className="flex-1 text-base text-foreground sm:text-lg">{title}</span>

        <div className="flex shrink-0 items-center justify-center">
          <div className="flex size-6 min-w-6 items-center justify-center rounded-full bg-primary sm:size-5 sm:min-w-5">
            {open ? (
              <FiX className="text-sm text-[#E1A8A0] sm:text-lg" />
            ) : (
              <FiPlus className="text-sm text-[#E1A8A0] sm:text-lg" />
            )}
          </div>
        </div>
      </button>
      {open && <p className="mt-2 text-sm leading-relaxed text-default-600 sm:mt-3 sm:text-base">{content}</p>}
      <hr className="mt-3 border-1 sm:mt-4" style={{ borderColor: "#D9D9D980" }} />
    </div>
  )
}
