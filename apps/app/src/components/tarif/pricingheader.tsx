"use client"
import React, { useState } from "react"

import { logger } from "@buildismart/lib"

import { PricingCard } from "./pricingcard"

const PricingHeader: React.FC = () => {
  const [activePlan, setActivePlan] = useState<"mensuel" | "annuel">("mensuel")

  const features = [
    { text: "Analyse de 5 DCE/mois", included: true },
    { text: "IA conversationnelle basique", included: true },
    { text: "Pas d’export PDF", included: false },
    { text: "Recherche avancée dans les documents", included: true },
  ]

  const handleSelect = (planName: string) => {
    logger.log(`Plan sélectionné : ${planName}`)
  }

  return (
    <div className="w-full">
      <div className="mb-6 flex justify-center px-4 sm:px-6 md:px-8">
        <div className="mb-8 flex justify-center">
          <div className="border-conten inline-flex rounded-full border p-1 shadow-sm sm:p-2">
            <button
              className={`rounded-full px-4 py-2 text-base font-semibold transition-all duration-200 sm:px-8 sm:py-3 sm:text-lg ${
                activePlan === "mensuel" ? "bg-primary text-white" : "bg-transparent text-gray-700"
              }`}
              onClick={() => setActivePlan("mensuel")}
            >
              Mensuel
            </button>
            <button
              className={`rounded-full px-4 py-2 text-base font-semibold transition-all duration-200 sm:px-8 sm:py-3 sm:text-lg ${
                activePlan === "annuel" ? "bg-primary text-content1" : "bg-transparent text-gray-700"
              }`}
              onClick={() => setActivePlan("annuel")}
            >
              Annuel
            </button>
          </div>
        </div>
      </div>

      <div>
        <div className="mb-8 flex justify-center px-4 sm:px-6 md:px-8">
          <span className="text-sm text-secondary sm:text-base">*2 mois offerts !</span>
        </div>
        <div className="flex flex-col items-center justify-center gap-6 px-4 sm:gap-x-6 sm:gap-y-8 md:px-12 lg:flex-row">
          <div className="w-full max-w-sm flex-1 sm:max-w-md md:max-w-lg">
            <PricingCard
              title="Standard"
              price={activePlan === "mensuel" ? 29 : 290}
              period={activePlan === "mensuel" ? "mois" : "an"}
              features={features}
              onSelect={() => handleSelect(`Standard ${activePlan}`)}
              variant="standard"
              showBadge={false}
            />
          </div>
          <div className="w-full max-w-sm flex-1 sm:max-w-md md:max-w-lg">
            <PricingCard
              title="Premium"
              price={activePlan === "mensuel" ? 49 : 490}
              period={activePlan === "mensuel" ? "mois" : "an"}
              features={features}
              onSelect={() => handleSelect(`Premium ${activePlan}`)}
              variant="premium"
              showBadge={activePlan === "annuel"}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default PricingHeader
