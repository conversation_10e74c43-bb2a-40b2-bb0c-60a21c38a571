import Link from "next/link"
import { <PERSON>a<PERSON>ace<PERSON><PERSON>, Fa<PERSON><PERSON><PERSON><PERSON>, FaXTwitter } from "react-icons/fa6"

export default function Footer() {
  return (
    <footer className="bg-primary text-white">
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col items-center justify-between md:flex-row">
          <div className="order-2 md:order-1 md:ml-[130px]">
            <div className="flex flex-wrap justify-center gap-x-6 gap-y-2">
              <Link href="/mentions-legales" className="text-sm font-medium hover:underline">
                Mentions légales
              </Link>
              <Link href="/politique-confidentialite" className="text-sm font-medium hover:underline">
                Politique de confidentialité
              </Link>
            </div>
          </div>

          <div className="order-1 mb-4 flex gap-4 md:order-2 md:mb-0">
            <a href="#" aria-label="Facebook" className="text-xl hover:text-blue-300">
              <FaFacebookF />
            </a>
            <a href="#" aria-label="Twitter" className="text-xl hover:text-blue-300">
              <FaXTwitter />
            </a>
            <a href="#" aria-label="Instagram" className="text-xl hover:text-blue-300">
              <FaInstagram />
            </a>
          </div>
        </div>
      </div>
    </footer>
  )
}
