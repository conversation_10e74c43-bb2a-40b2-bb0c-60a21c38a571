// components/chatcomponents/chatmessage.tsx
"use client"

import React from "react"

import { cn } from "@/lib/utils"

interface ChatMessageProps {
  role: "user" | "assistant"
  content: string
}

export function ChatMessage({ role, content }: ChatMessageProps) {
  const isUser = role === "user"

  return (
    <div className={cn("flex w-full", isUser ? "justify-end" : "justify-start")}>
      <div
        className={cn(
          "max-w-lg whitespace-pre-wrap rounded-xl p-3 text-sm shadow-md",
          isUser ? "bg-gray-100 text-gray-900" : "bg-white text-gray-800"
        )}
      >
        {content}
      </div>
    </div>
  )
}
