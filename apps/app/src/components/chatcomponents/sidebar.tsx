// components/chatcomponents/sidebar.tsx
"use client"
import React, { useEffect, useState } from "react"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { ChevronDown, ChevronRight, Folder as FolderIcon, Menu, Plus, Search, X } from "lucide-react"

import { FolderItem } from "@/components/chatcomponents/folderitem"
import {
  chunkAndEmbedDocument,
  deleteItem,
  getAllFolders,
  getFolderById,
  uploadFile,
  uploadFolder,
  upsertItem,
} from "@/lib/folder-utils"
import { extractTextFromImageAndSend } from "@/lib/utils/extractImageTextAndSend"
import { logger } from "@buildismart/lib"
import { Spinner } from "@nextui-org/react"

type FolderType = {
  id: string
  title: string
  createdAt: Date
  parentId: string | null
  type: "folder" | "file"
  url?: string
  textextraitfichier?: string // Texte extrait d'un fichier
  nombrefichiers?: number // ✅ ici tu mets le type
  summary?: string // Texte extrait d'un fichier
  texteextraitdossier?: string // Texte extrait de tout un dossier
  fileData?: File | null
  folderData?: { name: string; files: File[] } | null
  Ismodified?: boolean // Champ optionnel dans le type
}
interface SidebarProps {
  folders: FolderType[]
}

// Fonction externe modifiée
export const updateFolderModifiedStatus = async (
  folderId: string,
  isModified: boolean,
  folders: FolderType[],
  setFolders: React.Dispatch<React.SetStateAction<FolderType[]>>
) => {
  try {
    logger.log(folders)
    const folderToUpdate = folders.find((f) => f.id === folderId)

    if (!folderToUpdate) return

    const updatedFolder: FolderType = {
      ...folderToUpdate,
      Ismodified: false,
    }

    await upsertItem(updatedFolder)
    setFolders(folders.map((f) => (f.id === folderId ? updatedFolder : f)))

    return updatedFolder
  } catch (error) {
    console.error("Erreur lors de la mise à jour du statut modifié:", error)
    return null
  }
}

export function Sidebar({ folders: initialFolders }: SidebarProps) {
  const router = useRouter()
  const [collapsed, setCollapsed] = useState(false)
  const [isRedirecting, setIsRedirecting] = useState(false)

  const [isLoading, setIsLoading] = useState(false)
  const [loadingMessage, setLoadingMessage] = useState("")
  const [mobileOpen, setMobileOpen] = useState(false)
  const [folders, setFolders] = useState<FolderType[]>(initialFolders)
  const [activeId, setActiveId] = useState<string | null>(null)
  const [renamingId, setRenamingId] = useState<string | null>(null)
  const [renameValue, setRenameValue] = useState("")
  const [searchQuery, setSearchQuery] = useState("")
  const [showSearchInput, setShowSearchInput] = useState(false)
  const [showNotification, setShowNotification] = useState(false)
  const [notificationMessage, setNotificationMessage] = useState("")
  const [_isModalCreateFolderOpen, setIsModalCreateFolderOpen] = useState(false)
  const [showNewActionsMenu, setShowNewActionsMenu] = useState(false)
  const [uploadTargetFolderId, setUploadTargetFolderId] = useState<string | null>(null)
  const fileInputRef = React.useRef<HTMLInputElement>(null)
  const folderInputRef = React.useRef<HTMLInputElement>(null)

  const currentUploadFolderTargetId = React.useRef<string | null>(null)

  useEffect(() => {
    async function loadData() {
      setIsLoading(true)
      let data = null

      try {
        data = await getAllFolders()
        logger.log(data)
      } catch (error) {
        console.error("Erreur lors du chargement des dossiers :", error)
      } finally {
        setIsLoading(false)
        if (data) setFolders(data)
      }
    }

    loadData()
  }, [])

  // Removed localStorage caching of folders - data comes from database queries

  // Removed savedFolders functionality - projects load directly from database

  // Dans le sidebar.tsx, modifiez la fonction handleStartChatWithAI :
  const handleStartChatWithAI = async (itemId: string) => {
    const item = folders.find((f) => f.id === itemId)
    if (item) {
      try {
        setIsRedirecting(true)

        // Removed localStorage for currentChatItem - URL params handle navigation state

        // Attendre un tick du cycle d'événements pour s'assurer que l'UI est mise à jour
        await new Promise((resolve) => setTimeout(resolve, 0))
        const updatedFolders = await getAllFolders()
        setFolders(updatedFolders)
        await router.push(`/chat/${itemId}`)
      } finally {
        // S'assurer que l'état est réinitialisé même en cas d'erreur
        setIsRedirecting(false)
      }
    }
  }

  useEffect(() => {
    if (showNotification) {
      const timer = setTimeout(() => {
        setShowNotification(false)
        setNotificationMessage("")
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [showNotification])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.getElementById("mobile-sidebar")
      const toggle = document.getElementById("mobile-toggle")
      const newActionsMenu = document.getElementById("new-actions-menu")
      const newButton = document.getElementById("new-document-button")

      if (
        mobileOpen &&
        sidebar &&
        !sidebar.contains(event.target as Node) &&
        toggle &&
        !toggle.contains(event.target as Node)
      ) {
        setMobileOpen(false)
      }

      if (
        showNewActionsMenu &&
        newActionsMenu &&
        !newActionsMenu.contains(event.target as Node) &&
        newButton &&
        !newButton.contains(event.target as Node)
      ) {
        setShowNewActionsMenu(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [mobileOpen, showNewActionsMenu])

  const handleDeleteFolder = async (id: string) => {
    setIsDeleting(true)

    try {
      await deleteItem(id)
      const updatedFolders = await getAllFolders()
      logger.log(updatedFolders)
      setFolders(updatedFolders)
      // Removed savedFolders functionality

      if (activeId === id) setActiveId(null)

      setNotificationMessage("Élément supprimé avec tout son contenu !")
      setIsDeleting(false)

      setShowNotification(true)

      logger.log("Elements après suppression", await getAllFolders())
    } catch (error) {
      console.error("Erreur lors de la suppression:", error)
      setNotificationMessage("Erreur lors de la suppression !")
      setShowNotification(true)
    } finally {
      setIsDeleting(false)
    }
  }

  const handleRenameFolder = async (id: string) => {
    const currentItem = folders.find((f) => f.id === id)
    if (!currentItem || renameValue.trim() === "" || renameValue.trim() === currentItem.title) {
      setRenamingId(null)
      return
    }

    try {
      // Mettre à jour le state local immédiatement
      const updatedFolders = folders.map((f) => (f.id === id ? { ...f, title: renameValue.trim() } : f))
      setFolders(updatedFolders)

      // Sauvegarder en base de données
      const updatedItem = {
        ...currentItem,
        title: renameValue.trim(),
        Ismodified: true,
      }

      await upsertItem(updatedItem)

      // Rafraîchir depuis la base
      const allFolders = await getAllFolders()
      setFolders(allFolders)

      setNotificationMessage("Nom modifié avec succès !")
      setShowNotification(true)
    } catch (error) {
      console.error("Erreur lors du renommage:", error)
      setNotificationMessage("Erreur lors du renommage !")
      setShowNotification(true)
      // Revenir à l'ancienne valeur en cas d'erreur
      setFolders(folders)
    } finally {
      setRenamingId(null)
    }
  }

  // Removed savedFolders functionality - projects load directly from database

  const handleStartRename = (id: string, currentTitle: string) => {
    setRenameValue(currentTitle)
    setRenamingId(id)
  }

  const handleNewNestedFolder = async () => {
    const tempId = `temp-${Date.now()}`
    try {
      setIsLoading(true)

      // 1. Créer un dossier temporaire immédiatement
      const tempFolder: FolderType = {
        id: tempId,
        title: "Création...",
        createdAt: new Date(),
        parentId: null,
        type: "folder",
        Ismodified: false,
      }

      setFolders((prev) => [tempFolder, ...prev])

      // 2. Appel API
      const newFolder = await uploadFolder([], null)
      logger.log("Nouveau dossier créé:", newFolder)

      // 3. Mettre à jour avec les vraies données
      const updatedFolders = await getAllFolders()
      setFolders(updatedFolders)

      setNotificationMessage("Dossier créé !")
      setShowNotification(true)
    } catch (error) {
      console.error("Erreur:", error)
      // Retirer le dossier temporaire en cas d'erreur
      setFolders((prev) => prev.filter((f) => f.id !== tempId))
      setNotificationMessage("Erreur création dossier")
      setShowNotification(true)
    } finally {
      setIsLoading(false)
    }
  }

  const handleNewFile = (parentId: string | null) => {
    const newFile: FolderType = {
      id: Date.now().toString(),
      title: "Nouveau fichier",
      createdAt: new Date(),
      parentId: parentId,
      type: "file",
      url: "#",
    }
    setFolders([newFile, ...folders])
    setNotificationMessage("Nouveau fichier créé !")
    setShowNotification(true)
  }

  const handleUploadFile = async (file: File, parentId: string | null) => {
    if (!file) return
    setIsExtractingText(true)
    setLoadingMessage(`Téléchargement de ${file.name}...`)

    try {
      // Uploader le fichier vers S3
      const newFile = await uploadFile(file, parentId)
      let extractedText = ""
      let summary = ""

      if (file.type.startsWith("image/")) {
        const data = (await extractTextFromImageAndSend(file)) as { extractedText: string; summary: string }
        extractedText = data.extractedText || ""
        summary = data.summary || ""
      } else {
        const formData = new FormData()
        formData.append("file", file)

        const response = await fetch("/api/extract-text", {
          method: "POST",
          body: formData,
        })

        if (!response.ok) throw new Error("Erreur lors de l'extraction du texte")
        const data = (await response.json()) as { extractedText: string; summary: string }
        extractedText = data.extractedText || ""
        summary = data.summary || ""
      }

      // Mettre à jour le fichier avec les métadonnées extraites
      const updatedFile: FolderType = {
        ...newFile,
        textextraitfichier: extractedText,
        summary: summary,
        type: "file", // <-- Ajoutez cette ligne si elle n'est pas déjà présente
      }

      await upsertItem(updatedFile)
      await chunkAndEmbedDocument(newFile.id)

      const allFolders = await getAllFolders()

      // Fonction récursive pour mettre à jour tous les parents
      const updateParentChain = async (currentParentId: string | null) => {
        if (!currentParentId) return

        const parent = allFolders.find((f) => f.id === currentParentId && f.type === "folder")
        if (!parent) return

        // Mise à jour du parent actuel
        const updatedParent: FolderType = {
          ...parent,
          Ismodified: true,
          nombrefichiers: (parent.nombrefichiers || 0) + 1,
          texteextraitdossier: parent.texteextraitdossier
            ? `${parent.texteextraitdossier}\n\n${extractedText}`
            : extractedText,
        }

        await upsertItem(updatedParent)

        // Mise à jour récursive du parent du parent
        await updateParentChain(parent.parentId)
      }

      // Lancer la mise à jour en chaîne si parentId existe
      if (parentId) {
        await updateParentChain(parentId)
      }

      // Rafraîchir les données après toutes les mises à jour
      const updatedData = await getAllFolders()
      setFolders(updatedData)

      setNotificationMessage(`Fichier '${file.name}' ajouté avec succès !`)
      setShowNotification(true)
    } catch (error) {
      console.error("Erreur lors de l'upload:", error)
      setNotificationMessage(`Erreur lors de l'upload : ${(error as Error).message}`)
      setShowNotification(true)
    } finally {
      setIsExtractingText(false)
      setLoadingMessage("")
    }
  }

  const triggerFileUploadForFolder = (parentId: string) => {
    setUploadTargetFolderId(parentId)
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const getRelativeDate = (created: Date) => {
    const today = new Date()
    const diff = Math.floor((today.getTime() - created.getTime()) / (1000 * 60 * 60 * 24))
    if (diff === 0) return "Aujourd'hui"
    if (diff === 1) return "Hier"
    if (diff < 7) return `Il y a ${diff} jours`
    return created.toLocaleDateString()
  }

  const displayedItems = (Array.isArray(folders) ? folders : [])
    .filter((f) => f?.title?.toLowerCase().includes(searchQuery.toLowerCase()))

  const rootItems = displayedItems
    .filter((f) => f.parentId === null)
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())

  const groupedRootItems: Record<string, FolderType[]> = rootItems.reduce(
    (acc, folder) => {
      const label = getRelativeDate(folder.createdAt)
      if (!acc[label]) acc[label] = []
      acc[label].push(folder)
      return acc
    },
    {} as Record<string, FolderType[]>
  )

  /*const extractTextFromFileViaApi = async (
    file: File
  ): Promise<{ extractedText: string; summary: string }> => {
    let extractedText = "";
    let summary = "";

    if (file.type.startsWith("image/")) {
      // Utiliser la fonction OCR pour les images
      const data = await extractTextFromImageAndSend(file);
      extractedText = data.extractedText || "";
      summary = data.summary || "";
    } else {
      // Utiliser l'API backend pour les autres fichiers
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch("/api/extract-text", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Erreur lors de l'extraction du texte");
      }

      const data = (await response.json()) as { extractedText: string, summary: string };
      extractedText = data.extractedText || "";
      summary = data.summary || "";
    }

    return { extractedText, summary };
  };*/
  const handleGlobalFolderUpload = async (
    event: React.ChangeEvent<HTMLInputElement>,
    targetParentId: string | null
  ) => {
    const files = event.target.files
    if (!files || files.length === 0) {
      setNotificationMessage("Aucun fichier sélectionné pour l'upload de dossier.")
      setShowNotification(true)
      return
    }

    setIsExtractingText(true)
    setLoadingMessage(`Téléchargement de ${files.length} fichier(s)...`)

    try {
      // Uploader la structure du dossier

      const save = Array.from(files)
      const uploadedItems = await uploadFolder(Array.from(files), targetParentId)

      // Extraire le texte des fichiers
      const textExtractionPromises = save.map(async (file, index) => {
        const fileItem = uploadedItems[index]

        if (!fileItem) return null

        let extractedText = ""
        let summary = ""

        if (file.type.startsWith("image/")) {
          const data = (await extractTextFromImageAndSend(file)) as { extractedText: string; summary: string }
          extractedText = data.extractedText || ""
          summary = data.summary || ""
        } else {
          const formData = new FormData()
          formData.append("file", file)

          const response = await fetch("/api/extract-text", {
            method: "POST",
            body: formData,
          })

          if (!response.ok) throw new Error("Erreur lors de l'extraction du texte")
          const data = (await response.json()) as { extractedText: string; summary: string }
          extractedText = data.extractedText || ""
          summary = data.summary || ""
        }
        logger.log("Mettre à jour le fichier avec texte")
        // Mettre à jour le fichier avec le texte extrait
        const updatedFile: FolderType = {
          ...fileItem,
          textextraitfichier: extractedText,
          summary: summary,
          type: "file",
        }
        logger.log("fileItem.parentId:", fileItem.parentId)

        await upsertItem(updatedFile)
        await chunkAndEmbedDocument(fileItem.id)

        return { fileId: fileItem.id, extractedText, parentId: fileItem.parentId }
      })

      const extractionResults = await Promise.all(textExtractionPromises)
      logger.log("extractionResults")
      logger.log(extractionResults)

      // Mettre à jour les dossiers parents avec les textes extraits
      for (const result of extractionResults) {
        if (!result) continue

        let parentId = result.parentId
        while (parentId) {
          const parent = await getFolderById(parentId)
          if (!parent) break

          const updatedParent: FolderType = {
            ...parent,
            Ismodified: true,
            texteextraitdossier: parent.texteextraitdossier
              ? `${parent.texteextraitdossier}\n\n${result.extractedText}`
              : result.extractedText,
          }

          await upsertItem(updatedParent)
          parentId = parent.parentId
        }
      }

      // Rafraîchir les données
      const updatedFolders = await getAllFolders()
      setFolders(updatedFolders)
      logger.log(updatedFolders)
      setNotificationMessage(`Dossier uploadé avec succès !`)
      setShowNotification(true)
    } catch (error) {
      console.error("Erreur lors de l'upload:", error)
      setNotificationMessage(`Erreur lors de l'upload: ${(error as Error).message}`)
      setShowNotification(true)
    } finally {
      setIsExtractingText(false)
      setLoadingMessage("")
      setShowNewActionsMenu(false)
    }
  }
  const triggerFolderUploadForFolder = (parentId: string) => {
    currentUploadFolderTargetId.current = parentId
    if (folderInputRef.current) {
      folderInputRef.current.click()
    }
  }

  return (
    <>
      <button
        id="mobile-toggle"
        onClick={() => setMobileOpen(!mobileOpen)}
        className="fixed left-4 top-4 z-50 rounded-md bg-primary p-2 text-white shadow-lg md:hidden"
      >
        {mobileOpen ? <X size={20} /> : <Menu size={20} />}
      </button>

      {mobileOpen && <div className="fixed inset-0 z-40 bg-black/50 md:hidden" />}

      <aside
        id="mobile-sidebar"
        className={`fixed z-50 flex h-screen flex-col bg-primary transition-all duration-300 md:relative md:z-auto ${mobileOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"
          } ${collapsed ? "w-16 md:w-16" : "w-4/5 sm:w-2/3 md:w-1/5"} `}
      >
        <div className="flex items-center justify-between p-2 sm:px-4">
          {!collapsed ? (
            <Image
              src="/l.png"
              alt="Logo"
              width={230}
              height={230}
              className="ml-2 block h-auto w-32 sm:ml-4 sm:w-48 md:w-[230px]"
            />
          ) : (
            <div className="w-8"></div>
          )}

          <button onClick={() => setCollapsed(!collapsed)} className="hidden shrink-0 md:block">
            <Image src="/col.png" alt="Toggle sidebar" width={60} height={60} className="size-6 sm:size-8" />
          </button>
        </div>

        <div className="mt-2 flex flex-1 flex-col gap-2 overflow-y-auto p-2 px-4 sm:gap-4 sm:p-4 sm:px-8">
          {!collapsed ? (
            <>
              <div className="relative">
                <button
                  id="new-document-button"
                  onClick={() => setShowNewActionsMenu(!showNewActionsMenu)}
                  className="flex w-full items-center gap-2 rounded bg-primary px-3 py-2 text-sm text-white transition-colors duration-200 hover:bg-white/20 sm:px-5 sm:text-lg"
                >
                  <Plus size={16} className="sm:size-5" color="white" />
                  <span className="hidden sm:inline">Nouveau</span>
                  <span className="sm:hidden">Nouveau</span>
                  {showNewActionsMenu ? (
                    <ChevronDown size={16} className="ml-auto" />
                  ) : (
                    <ChevronRight size={16} className="ml-auto" />
                  )}
                </button>

                {showNewActionsMenu && (
                  <div
                    id="new-actions-menu"
                    className="absolute inset-x-0 top-full z-10 mt-1 rounded bg-white py-1 shadow-lg"
                  >
                    <button
                      onClick={() => {
                        setIsModalCreateFolderOpen(true)
                        setShowNewActionsMenu(false)
                        // Appeler handleNewNestedFolder avec null comme parentId pour un nouveau dossier racine
                        handleNewNestedFolder()
                      }}
                      className="flex w-full items-center gap-2 px-4 py-2 text-sm text-black hover:bg-gray-100"
                    >
                      <FolderIcon size={16} /> Nouveau projet
                    </button>
                    <label
                      htmlFor="upload-folder-input"
                      className="flex w-full cursor-pointer items-center gap-2 px-4 py-2 text-sm text-black hover:bg-gray-100"
                    >
                      <FolderIcon size={16} /> Télécharger un dossier
                      <input
                        id="upload-folder-input"
                        type="file"
                        // @ts-expect-error: webkitdirectory est une propriété non-standard mais couramment utilisée pour les dossiers
                        webkitdirectory="true"
                        directory=""
                        multiple
                        onChange={(e) => handleGlobalFolderUpload(e, null)}
                        className="hidden"
                      />
                    </label>
                  </div>
                )}
              </div>
              {(isLoading || isExtractingText || isDeleting || isRedirecting) && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
                  <div className="bg-white rounded-lg p-6 shadow-lg">
                    <Spinner size="lg" />
                    <p className="mt-3 text-center font-medium">
                      {loadingMessage ||
                        (isExtractingText ? "Upload en cours..." :
                          isDeleting ? "Suppression en cours..." :
                            isRedirecting ? "Redirection..." :
                              "Chargement...")}
                    </p>
                  </div>
                </div>
              )}
              <div>
                {showSearchInput ? (
                  <input
                    type="text"
                    autoFocus
                    className="w-full rounded bg-white px-2 py-1 text-xs text-black sm:text-sm"
                    placeholder="Rechercher un dossier..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onBlur={() => {
                      if (!searchQuery) setShowSearchInput(false)
                    }}
                  />
                ) : (
                  <button
                    type="button"
                    onClick={() => setShowSearchInput(true)}
                    className="flex w-full items-center gap-2 rounded bg-primary px-3 py-2 text-sm text-white transition-colors duration-200 hover:bg-white/20 sm:px-5 sm:text-lg"
                  >
                    <Search size={16} className="sm:size-5" color="white" />
                    Rechercher
                  </button>
                )}
              </div>

              {/* Removed savedFolders bookmark functionality */}

              {showNotification && (
                <div className="fixed right-4 top-16 z-50 rounded-md bg-primary px-3 py-2 text-sm text-white shadow-lg sm:top-4 sm:px-4 sm:text-base">
                  {notificationMessage}
                </div>
              )}

              <input
                type="file"
                ref={fileInputRef}
                onChange={(e) => {
                  if (e.target.files && e.target.files.length > 0 && uploadTargetFolderId) {
                    handleUploadFile(e.target.files[0], uploadTargetFolderId)
                    setUploadTargetFolderId(null)
                    e.target.value = ""
                  }
                }}
                className="hidden"
              />
              <input
                type="file"
                ref={folderInputRef}
                // @ts-expect-error: webkitdirectory est une propriété non-standard
                webkitdirectory="true"
                directory=""
                multiple
                onChange={(e) => {
                  if (e.target.files && e.target.files.length > 0) {
                    handleGlobalFolderUpload(e, currentUploadFolderTargetId.current)
                    currentUploadFolderTargetId.current = null
                    e.target.value = ""
                  }
                }}
                className="hidden"
              />

              {Object.entries(groupedRootItems).map(([label, items]) => (
                <div key={label}>
                  <div className="mt-4 text-xs text-gray-200 sm:text-sm">{label}</div>
                  <div className="flex flex-col gap-1 sm:gap-2">
                    {items.map((f) => (
                      <FolderItem
                        key={f.id}
                        folder={f}
                        allFolders={displayedItems}
                        activeId={activeId}
                        renamingId={renamingId}
                        renameValue={renameValue}
                        setActiveId={setActiveId}
                        setRenamingId={setRenamingId}
                        setRenameValue={setRenameValue}
                        handleRenameFolder={handleRenameFolder}
                        handleDeleteFolder={handleDeleteFolder}
                        handleStartRename={handleStartRename}
                        handleNewFile={handleNewFile}
                        onUploadFile={triggerFileUploadForFolder}
                        onUploadFolder={triggerFolderUploadForFolder}
                        onMobileClose={() => setMobileOpen(false)}
                        onStartChat={handleStartChatWithAI}
                        activeChatId={null}
                        handleNewNestedFolder={function (): void {
                          throw new Error("Function not implemented.")
                        }}
                      />
                    ))}
                  </div>
                </div>
              ))}
            </>
          ) : (
            <button
              onClick={() => setCollapsed(false)}
              className="flex items-center justify-center rounded p-2 text-white transition-colors duration-200 hover:bg-white/20"
            >
              <Search size={16} className="sm:size-[18px]" />
            </button>
          )}
        </div>

        {!collapsed && (
          <div className="h-32 px-3 sm:h-40 sm:px-5">
            <div className="relative m-2 overflow-hidden rounded-lg bg-gray100/70 p-3 text-xs text-black sm:m-4 sm:p-4 sm:text-sm">
              <div className="opacity-8 pointer-events-none absolute inset-0 mt-12 bg-[url('/Vector.png')] bg-cover bg-bottom" />

              <div className="relative z-10 flex size-full flex-col justify-center">
                <p className="mb-2">
                  Accédez à l&rsquo;analyse illimitée de vos appels d&rsquo;offres, aux exports intelligents, et à bien
                  plus encore.
                </p>
                <div className="flex justify-center">
                  <button
                    onClick={() => router.push("/")}
                    className="rounded bg-secondary px-2 py-1 text-xs text-black transition-colors hover:bg-couleur-tertiaire sm:px-3 sm:text-sm"
                  >
                    <span className="hidden sm:inline">Passer au plan Premium</span>
                    <span className="sm:hidden">Premium</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </aside>
    </>
  )
}
