"use client"

import React, { useEffect, useRef, useState } from "react"
import Image from "next/image"
import { signOut, useSession } from "next-auth/react"
import { Settings } from "lucide-react"

export function Topbar() {
  const { data: session } = useSession()
  const [showSettings, setShowSettings] = useState(false)
  const settingsRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (settingsRef.current && !settingsRef.current.contains(event.target as Node)) {
        setShowSettings(false)
      }
    }
    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  // Récupération des données utilisateur
  const userName = session?.user?.name || "Utilisateur"
  const userImage = session?.user?.image || "/avatarr.png"

  return (
    <header className="sticky top-0 z-10 flex w-full items-center justify-between bg-white p-4 shadow-md">
      {/* À gauche : Avatar + nom + message */}
      <div className="flex items-center gap-4">
        <Image src={userImage} alt="avatar" width={48} height={48} className="rounded-full" />
        <div className="flex flex-col">
          <span className="text-base font-bold text-black">{userName}</span>
          <span className="text-sm text-gray500">Ravi de vous revoir</span>
        </div>
      </div>

      <div className="relative" ref={settingsRef}>
        <button onClick={() => setShowSettings(!showSettings)} className="rounded-full p-2 hover:bg-gray100">
          <Settings className="text-gray500" />
        </button>

        {/* Menu déroulant */}
        {showSettings && (
          <div className="absolute right-0 z-20 mt-2 w-48 rounded border bg-content1 shadow-lg">
            <ul className="text-sm text-gray500">
              <li className="cursor-pointer px-4 py-2 hover:bg-gray100">Mon profil</li>
              <li className="cursor-pointer px-4 py-2 hover:bg-gray100">Préférences</li>
              <li className="cursor-pointer px-4 py-2 hover:bg-gray100" onClick={() => signOut({ callbackUrl: "/" })}>
                Se déconnecter
              </li>
            </ul>
          </div>
        )}
      </div>
    </header>
  )
}
