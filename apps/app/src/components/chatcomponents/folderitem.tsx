"use client"

import React, { useState } from "react"
import { ChevronDown, ChevronRight, FileText, Folder as FolderIcon, MessageCircle } from "lucide-react"

import FolderDropdown from "@/components/chatcomponents/folderdropdown"

type FolderType = {
  id: string
  title: string
  createdAt: Date
  parentId: string | null
  type: "folder" | "file"
  url?: string
}

type FolderItemProps = {
  folder: FolderType
  allFolders: FolderType[]
  activeId: string | null
  renamingId: string | null
  renameValue: string
  savedFolders: string[]
  setActiveId: (id: string | null) => void
  setRenamingId: (id: string | null) => void
  setRenameValue: (value: string) => void
  handleRenameFolder: (id: string) => void
  handleDeleteFolder: (id: string) => void
  handleToggleSave: (id: string) => void
  handleStartRename: (id: string, currentTitle: string) => void
  handleNewNestedFolder: (parentId: string) => void
  handleNewFile: (parentId: string) => void
  onUploadFile: (parentId: string) => void
  onUploadFolder: (parentId: string) => void
  onMobileClose?: () => void
  onStartChat: (itemId: string) => void
  activeChatId: string | null
  isDeleting?: boolean
}

export function FolderItem({
  folder,
  allFolders,
  activeId,
  renamingId,
  renameValue,
  savedFolders,
  setActiveId,
  setRenamingId,
  setRenameValue,
  handleRenameFolder,
  handleDeleteFolder,
  handleToggleSave,
  handleStartRename,
  handleNewNestedFolder,
  handleNewFile,
  onUploadFile,
  onUploadFolder,
  onMobileClose,
  onStartChat,
  activeChatId,
}: FolderItemProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const isParent = allFolders.some((item) => item.parentId === folder.id)

  const childItems = folder.type === "folder" ? allFolders.filter((item) => item.parentId === folder.id) : []

  const handleChatClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onStartChat(folder.id)
  }

  const isActiveChat = activeChatId === folder.id

  const getTruncatedTitle = (title: string, wordLimit: number = 3) => {
    const words = title.split(" ")
    if (words.length > wordLimit) {
      return words.slice(0, wordLimit).join(" ") + "..."
    }
    return title
  }

  return (
    <div className="flex flex-col">
      <div className="group relative flex w-full items-center">
        {folder.type === "folder" && isParent ? (
          <button
            onClick={(e) => {
              e.stopPropagation()
              setIsExpanded(!isExpanded)
            }}
            className="mr-1 shrink-0 rounded-sm p-1 text-white hover:bg-white/10"
            aria-label={isExpanded ? "Collapse folder" : "Expand folder"}
          >
            {isExpanded ? <ChevronDown size={14} /> : <ChevronRight size={14} />}
          </button>
        ) : (
          <div className="w-[22px] shrink-0" />
        )}

        <button
          className={`flex grow items-center gap-2 rounded p-2 text-left text-sm transition-colors duration-200 sm:text-base ${activeId === folder.id || isActiveChat ? "bg-white/20" : "hover:bg-white/20"
            } relative`}
          onClick={() => {
            setActiveId(folder.id)
            if (folder.type === "folder" && isParent) {
              setIsExpanded(!isExpanded)
            }
            onMobileClose?.()
          }}
          onDoubleClick={(e) => {
            // Empêcher le renommage pour les projets (dossiers racine)
            if (folder.parentId !== null) {
              handleStartRename(folder.id, folder.title)
            } else {
              e.preventDefault()
            }
          }}
        >
          {folder.type === "folder" ? (
            <FolderIcon size={16} className="sm:size-[18px]" color="white" />
          ) : (
            <FileText size={16} className="sm:size-[18px]" color="white" />
          )}

          {renamingId === folder.id ? (
            <input
              autoFocus
              className="w-full rounded bg-white px-1 py-0.5 text-xs text-black sm:text-sm"
              value={renameValue}
              onChange={(e) => setRenameValue(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.currentTarget.blur()
                }
              }}
              onBlur={() => handleRenameFolder(folder.id)}
              onClick={(e) => e.stopPropagation()}
            />
          ) : (
            <div className="flex w-full items-center justify-between">
              <span className="flex-1 break-words pr-2 text-sm text-white sm:text-base">
                {getTruncatedTitle(folder.title)}
              </span>

              <div className="flex shrink-0 items-center gap-2">
                {renamingId !== folder.id && folder.parentId !== null && folder.type !== "file" && (
                  <div className="px-8">
                    <button
                      onClick={handleChatClick}
                      className="focus:ring-opacity/50 shrink-0 rounded-full p-1 text-white transition-colors duration-200 hover:bg-white/20 focus:outline-none focus:ring-2 focus:ring-white sm:p-1.5"
                      aria-label="Discuter maintenant avec l'IA"
                    >
                      <MessageCircle size={15} className="sm:size-[18px]" />
                    </button>
                    <span className="absolute bottom-full left-1/2 mb-2 hidden w-max -translate-x-1/2 rounded bg-gray-700 px-2 py-1 text-xs text-white opacity-0 transition-opacity duration-200 group-hover/chat-bubble:block group-hover/chat-bubble:opacity-100">
                      Discuter maintenant
                    </span>
                  </div>
                )}

                {renamingId !== folder.id && (
                  <FolderDropdown
                    folderId={folder.id}
                    folderTitle={folder.title}
                    isSaved={savedFolders.includes(folder.id)}
                    type={folder.type}
                    folder={folder}
                    onDelete={handleDeleteFolder}
                    onToggleSave={handleToggleSave}
                    onStartRename={handleStartRename}
                    onNewNestedFolder={handleNewNestedFolder}
                    onNewFile={handleNewFile}
                    onUploadFile={onUploadFile}
                    onUploadFolder={onUploadFolder}
                  />
                )}
              </div>
            </div>
          )}
        </button>
      </div>

      {folder.type === "folder" && isExpanded && childItems.length > 0 && (
        <div className="ml-6">
          {childItems.map((childItem) => (
            <FolderItem
              key={childItem.id}
              folder={childItem}
              allFolders={allFolders}
              activeId={activeId}
              renamingId={renamingId}
              renameValue={renameValue}
              savedFolders={savedFolders}
              setActiveId={setActiveId}
              setRenamingId={setRenamingId}
              setRenameValue={setRenameValue}
              handleRenameFolder={handleRenameFolder}
              handleDeleteFolder={handleDeleteFolder}
              handleToggleSave={handleToggleSave}
              handleStartRename={handleStartRename}
              handleNewNestedFolder={handleNewNestedFolder}
              handleNewFile={handleNewFile}
              onUploadFile={onUploadFile}
              onUploadFolder={onUploadFolder}
              onMobileClose={onMobileClose}
              onStartChat={onStartChat}
              activeChatId={activeChatId}
            />
          ))}
        </div>
      )}
    </div>
  )
}
