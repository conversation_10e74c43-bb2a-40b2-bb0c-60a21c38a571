"use client"

import { useState } from "react"

import { <PERSON>ton, Input, Modal, ModalBody, ModalContent, <PERSON>dal<PERSON>ooter, ModalHeader } from "@nextui-org/react"

type ModalCreateFolderProps = {
  isOpen: boolean
  onClose: () => void
  onCreate: (folderName: string) => void // Cette fonction reçoit le nom du dossier
}

export default function ModalCreateFolder({ isOpen, onClose, onCreate }: ModalCreateFolderProps) {
  const [folderName, setFolderName] = useState("")

  const handleCreate = () => {
    if (folderName.trim()) {
      onCreate(folderName.trim())
      setFolderName("")
      onClose()
    }
  }

  const handleCancel = () => {
    setFolderName("")
    onClose()
  }

  return (
    <Modal isOpen={isOpen} onOpenChange={onClose} backdrop="opaque" placement="center">
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">Créer un nouveau dossier</ModalHeader>
        <ModalBody>
          <Input
            type="text"
            label="Nom du dossier"
            placeholder="Entrez le nom du dossier"
            value={folderName}
            onChange={(e) => setFolderName(e.target.value)}
            isRequired
            onKeyPress={(e) => {
              // Ajout pour valider avec Entrée
              if (e.key === "Enter") {
                handleCreate()
              }
            }}
          />
        </ModalBody>
        <ModalFooter>
          <Button variant="light" onPress={handleCancel}>
            Annuler
          </Button>
          <Button color="primary" onPress={handleCreate}>
            Créer
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}
