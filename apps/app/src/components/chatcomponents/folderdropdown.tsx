"use client"

import React from "react"
import { Bookmark, FilePlus, FolderUp, MoreVertical, Pen<PERSON>l, Trash2 } from "lucide-react"

import { Button, Dropdown, DropdownItem, DropdownMenu, DropdownSection, DropdownTrigger } from "@nextui-org/react"

type FolderDropdownProps = {
  folderId: string
  folderTitle: string
  isSaved: boolean
  type: "folder" | "file" // Ajout du type
  onDelete: (id: string) => void
  onToggleSave: (id: string) => void
  onStartRename: (id: string, currentTitle: string) => void
  onNewNestedFolder: (parentId: string) => void
  onNewFile: (parentId: string) => void
  onUploadFile: (parentId: string) => void
  onUploadFolder: (parentId: string) => void
}

export default function FolderDropdown({
  folderId,
  folderTitle,
  isSaved,
  type,
  onDelete,
  onToggleSave,
  onStartRename,
  onUploadFile,
  onUploadFolder,
}: FolderDropdownProps) {
  const handleStartRenameWrapper = () => {
    if (folderTitle !== "Dossier principal") {
      onStartRename(folderId, folderTitle)
    }
  }

  const handleDeleteWrapper = () => {
    if (folderTitle !== "Dossier principal") {
      onDelete(folderId)
    }
  }

  return (
    <Dropdown placement="bottom-end">
      <DropdownTrigger>
        <Button
          isIconOnly
          variant="light"
          className="min-w-unit-0 absolute right-1 top-1/2 z-10 -translate-y-1/2 p-1"
          aria-label={`Options pour ${type === "folder" ? "le dossier" : "le fichier"} ${folderTitle}`}
        >
          <MoreVertical size={14} className="sm:size-4" color="white" />
        </Button>
      </DropdownTrigger>

      <DropdownMenu aria-label={`Actions du ${type === "folder" ? "dossier" : "fichier"}`}>
        {/* Afficher ces options seulement pour les dossiers */}
        <DropdownSection showDivider>
          {type === "folder" ? (
            <>
              <DropdownItem
                key="upload-file"
                onPress={() => onUploadFile(folderId)}
                startContent={<FilePlus size={16} />}
              >
                Télécharger un fichier
              </DropdownItem>
              <DropdownItem
                key="upload-folder"
                onPress={() => onUploadFolder(folderId)}
                startContent={<FolderUp size={16} />}
              >
                Télécharger un dossier
              </DropdownItem>
            </>
          ) : (
            <></>
          )}
          <DropdownItem
            key="rename"
            onPress={handleStartRenameWrapper}
            startContent={<Pencil size={16} />}
            isDisabled={folderTitle === "Dossier principal"}
          >
            Renommer
          </DropdownItem>
          <DropdownItem key="toggle-save" onPress={() => onToggleSave(folderId)} startContent={<Bookmark size={16} />}>
            {isSaved ? "Retirer des sauvegardes" : "Sauvegarder"}
          </DropdownItem>
        </DropdownSection>

        <DropdownSection>
          <DropdownItem
            key="delete"
            className="text-danger"
            color="danger"
            onPress={handleDeleteWrapper}
            startContent={<Trash2 size={16} />}
            isDisabled={folderTitle === "Dossier principal"}
          >
            Supprimer
          </DropdownItem>
        </DropdownSection>
      </DropdownMenu>
    </Dropdown>
  )
}
