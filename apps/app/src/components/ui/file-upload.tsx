"use client"

import { InputHTMLAttributes, useCallback, useEffect, useState } from "react"
import { Crop, Upload } from "lucide-react"
import { Accept, useDropzone } from "react-dropzone"
import { toast } from "react-toastify"

import { TDictionary } from "@/lib/langs"
import { bytesToMegabytes, cn } from "@/lib/utils"
import { Button } from "@nextui-org/button"
import { useDisclosure } from "@nextui-org/modal"

import { Icons } from "../icons"

import { FileDr, FileUploadDr } from "./file-upload.dr"
import ImageCrop from "./image-crop"

function File({
  file,
  i,
  removeFile,
  handleCrop,
  dictionary,
}: {
  file: File
  i: number
  removeFile: (index: number) => void
  handleCrop: (index: number, file: File) => void
  dictionary: TDictionary<typeof FileDr>
}) {
  const { isOpen: isCroppingOpen, onOpen: onCroppingOpen, onOpenChange: onCroppingOpenChange } = useDisclosure()

  const setFile = useCallback(
    (file: File) => {
      handleCrop(i, file)
    },
    [handleCrop, i]
  )

  return (
    <li className="flex flex-col gap-2" key={i}>
      <div className="border-muted-foreground/30 flex flex-row items-center justify-between gap-2 rounded-medium border p-2">
        <div className="flex min-w-0 flex-1 flex-col gap-1 sm:flex-row sm:items-center">
          <span className="block truncate font-medium text-sm">{file.name}</span>
          <span className="text-muted-foreground text-xs sm:ml-2">
            ({bytesToMegabytes(file.size, true)}Mo)
          </span>
        </div>
        <div className="flex shrink-0 gap-1">
          <Button color="primary" className="h-8 min-w-0 w-8 rounded-full p-0" onPress={onCroppingOpen}>
            <Crop className="size-4" />
          </Button>
          <Button
            color="danger"
            className="h-8 min-w-0 w-8 rounded-full p-0"
            onPress={() => removeFile(i)}
          >
            <Icons.trash className="size-4" />
          </Button>
        </div>
      </div>
      <ImageCrop
        originalFile={file}
        setFile={setFile}
        onOpenChange={onCroppingOpenChange}
        isOpen={isCroppingOpen}
        dictionary={dictionary}
      />
    </li>
  )
}

export type TFileUploadProps = Omit<
  InputHTMLAttributes<HTMLInputElement>,
  "className" | "onFilesChange" | "dictionary" | "disabled" | "accept" | "dictionary"
> & {
  className?: string
  onFilesChange?: (files: File[]) => void
  disabled?: boolean
  accept?: Accept
  maxFiles?: number
  dictionary: TDictionary<typeof FileUploadDr>
  acceptCamera?: boolean
}

export default function FileUpload({
  className,
  onFilesChange,
  disabled,
  accept,
  maxFiles,
  dictionary,
  acceptCamera,
  ...props
}: TFileUploadProps) {
  const { acceptedFiles, getRootProps, getInputProps, isDragAccept, isDragReject } = useDropzone({
    accept,
    maxFiles,
    multiple: maxFiles !== 1,
    onDropRejected(fileRejections) {
      const fileRejection = fileRejections[0]
      if (fileRejection.errors[0].code === "file-invalid-type") {
        toast.error(dictionary.invalidFileType)
      }
    },
  })

  const [files, setFiles] = useState<File[]>([])
  const [croppedFiles, setCroppedFiles] = useState<File[]>([])

  useEffect(() => {
    if (!acceptedFiles.length) return
    // @ts-expect-error Error from boilerplate, must not be an handicap
    onFilesChange?.(acceptedFiles)
    // @ts-expect-error Error from boilerplate, must not be an handicap
    setFiles(acceptedFiles)
    // @ts-expect-error Error from boilerplate, must not be an handicap
    setCroppedFiles(acceptedFiles)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [acceptedFiles])

  const removeFile = (index: number) => {
    const newFiles = [...files]
    newFiles.splice(index, 1)
    onFilesChange?.(newFiles)
    setFiles(newFiles)
    setCroppedFiles(newFiles)
  }

  const handleCrop = useCallback(
    async (index: number, file: File) => {
      const newFiles = [...files]
      newFiles.splice(index, 1, file)
      onFilesChange?.(newFiles)
      setCroppedFiles(newFiles)
    },
    [files, onFilesChange]
  )

  return (
    <div className="flex flex-col gap-2">
      <div
        {...getRootProps()}
        className={cn(
          "bg-muted/20 flex h-[250px] cursor-pointer flex-col items-center justify-center gap-4 rounded-medium border border-dashed border-transparent p-2 px-6 text-foreground transition-all",
          {
            "hover:bg-muted/40 focus:bg-muted/40 hover:border-primary focus:border-primary": !disabled,
            "bg-muted/50 border-primary": isDragAccept,
            "border-danger bg-danger/40": isDragReject,
          },
          className
        )}
      >
        <input
          type="file"
          {...getInputProps()}
          disabled={disabled}
          accept={accept + (acceptCamera ? ";capture=camera" : "")}
          {...props}
        />
        <Upload className="size-12" />
        <p className="text-center text-sm text-foreground/80">{dictionary.uploadDescription}</p>
      </div>
      <ul className="flex flex-col gap-2">
        {croppedFiles.map((file, i) => (
          <File file={file} i={i} removeFile={removeFile} handleCrop={handleCrop} key={i} dictionary={dictionary} />
        ))}
      </ul>
    </div>
  )
}
