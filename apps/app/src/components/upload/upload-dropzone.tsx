"use client"

import React, { use<PERSON><PERSON>back, useState } from "react"
import { Upload, FileText, Folder, AlertCircle } from "lucide-react"
import { useDropzone } from "react-dropzone"

import { Button } from "@nextui-org/button"

interface UploadDropzoneProps {
  onFilesSelected: (files: File[]) => void
  onFolderSelected: (files: File[]) => void
  accept?: string[]
  maxSize?: number
  disabled?: boolean
  className?: string
}

export function UploadDropzone({
  onFilesSelected,
  onFolderSelected,
  accept = [".pdf", ".doc", ".docx", ".txt", ".jpg", ".jpeg", ".png"],
  maxSize = 10 * 1024 * 1024, // 10MB
  disabled = false,
  className = "",
}: UploadDropzoneProps) {
  const [dragType, setDragType] = useState<"files" | "folder" | null>(null)

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    if (rejectedFiles.length > 0) {
      // Handle rejected files
      const errors = rejectedFiles.map(rejection => 
        rejection.errors.map((error: any) => error.message).join(", ")
      ).join("; ")
      console.error("Fichiers rejetés:", errors)
      return
    }

    if (acceptedFiles.length > 0) {
      // Check if files are from a folder structure
      const hasFolder = acceptedFiles.some(file => 
        (file as any).webkitRelativePath && (file as any).webkitRelativePath.includes("/")
      )

      if (hasFolder) {
        onFolderSelected(acceptedFiles)
      } else {
        onFilesSelected(acceptedFiles)
      }
    }
  }, [onFilesSelected, onFolderSelected])

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: accept.reduce((acc, ext) => {
      const mimeType = getMimeType(ext)
      if (mimeType) {
        acc[mimeType] = [ext]
      }
      return acc
    }, {} as Record<string, string[]>),
    maxSize,
    disabled,
    onDragEnter: () => {
      // Try to detect if it's a folder drag (this is limited by browser security)
      setDragType("files") // Default to files
    },
    onDragLeave: () => {
      setDragType(null)
    },
  })

  const formatFileSize = (bytes: number): string => {
    const sizes = ['B', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const getMimeType = (extension: string): string | null => {
    const mimeTypes: Record<string, string> = {
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.txt': 'text/plain',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
    }
    return mimeTypes[extension.toLowerCase()] || null
  }

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    if (files.length > 0) {
      onFilesSelected(files)
    }
    // Reset input
    event.target.value = ""
  }

  const handleFolderInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    if (files.length > 0) {
      onFolderSelected(files)
    }
    // Reset input
    event.target.value = ""
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Dropzone */}
      <div
        {...getRootProps()}
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 cursor-pointer
          ${isDragActive && !isDragReject 
            ? "border-blue-400 bg-blue-50" 
            : isDragReject 
            ? "border-red-400 bg-red-50" 
            : disabled 
            ? "border-gray-200 bg-gray-50 cursor-not-allowed" 
            : "border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100"
          }
        `}
      >
        <input {...getInputProps()} disabled={disabled} />
        
        <div className="flex flex-col items-center gap-4">
          {isDragReject ? (
            <>
              <AlertCircle className="size-12 text-red-500" />
              <div>
                <p className="text-lg font-medium text-red-700">Fichiers non supportés</p>
                <p className="text-sm text-red-600">
                  Formats acceptés: {accept.join(", ")}
                </p>
                <p className="text-sm text-red-600">
                  Taille maximale: {formatFileSize(maxSize)}
                </p>
              </div>
            </>
          ) : isDragActive ? (
            <>
              <Upload className="size-12 text-blue-500 animate-bounce" />
              <div>
                <p className="text-lg font-medium text-blue-700">Déposez vos fichiers ici</p>
                <p className="text-sm text-blue-600">
                  Fichiers ou dossiers acceptés
                </p>
              </div>
            </>
          ) : (
            <>
              <Upload className="size-12 text-gray-400" />
              <div>
                <p className="text-lg font-medium text-gray-700">
                  Glissez-déposez vos fichiers ou dossiers ici
                </p>
                <p className="text-sm text-gray-500">
                  ou cliquez pour sélectionner
                </p>
                <p className="text-xs text-gray-400 mt-2">
                  Formats: {accept.join(", ")} • Max: {formatFileSize(maxSize)}
                </p>
              </div>
            </>
          )}
        </div>

        {disabled && (
          <div className="absolute inset-0 bg-gray-100/50 rounded-lg flex items-center justify-center">
            <p className="text-gray-500 font-medium">Upload désactivé</p>
          </div>
        )}
      </div>

      {/* Manual Selection Buttons */}
      <div className="flex gap-3 justify-center">
        <div className="relative">
          <Button
            color="primary"
            variant="bordered"
            startContent={<FileText className="size-4" />}
            disabled={disabled}
            className="relative overflow-hidden"
          >
            Sélectionner des fichiers
            <input
              type="file"
              multiple
              accept={accept.join(",")}
              onChange={handleFileInputChange}
              disabled={disabled}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            />
          </Button>
        </div>

        <div className="relative">
          <Button
            color="primary"
            variant="bordered"
            startContent={<Folder className="size-4" />}
            disabled={disabled}
            className="relative overflow-hidden"
          >
            Sélectionner un dossier
            <input
              type="file"
              // @ts-expect-error: webkitdirectory is a non-standard but widely supported property
              webkitdirectory="true"
              directory=""
              multiple
              onChange={handleFolderInputChange}
              disabled={disabled}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            />
          </Button>
        </div>
      </div>

      {/* Help Text */}
      <div className="text-center">
        <p className="text-xs text-gray-500">
          Vous pouvez télécharger plusieurs fichiers à la fois ou un dossier complet.
          <br />
          Les fichiers seront automatiquement organisés et traités.
        </p>
      </div>
    </div>
  )
}
