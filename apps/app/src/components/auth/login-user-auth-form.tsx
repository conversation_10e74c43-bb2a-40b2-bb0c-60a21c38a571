"use client"

import * as React from "react"
import { useForm } from "react-hook-form"
import * as z from "zod"

import { signInSchema } from "@/api/auth/schemas"
import { authRoutes } from "@/constants/auth"
import { handleSignError, handleSignIn } from "@/lib/auth/handle-sign"
import { TDictionary } from "@/lib/langs"
import { cn, ensureRelativeUrl } from "@/lib/utils"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@nextui-org/button"
import { Link } from "@nextui-org/link"

import TotpVerificationModal from "../profile/totp/totp-verification-modal"
import FormField from "../ui/form"

import { LoginUserAuthFormDr } from "./login-user-auth-form.dr"

type UserAuthFormProps = React.HTMLAttributes<HTMLFormElement> & {
  searchParams: { [key: string]: string | string[] | undefined }
  dictionary: TDictionary<typeof LoginUserAuthFormDr>
}

const formSchema = signInSchema

type IForm = z.infer<ReturnType<typeof formSchema>>

export function LoginUserAuthForm({ searchParams, dictionary, ...props }: UserAuthFormProps) {
  const callbackUrl = ensureRelativeUrl(searchParams.callbackUrl?.toString()) || authRoutes.redirectAfterSignIn
  const error = searchParams.error?.toString()

  const [isLoading, setIsLoading] = React.useState<boolean>(false)

  React.useEffect(() => {
    if (error && !error.startsWith("_")) {
      handleSignError(error, dictionary)
    }
  }, [error, dictionary])

  const form = useForm<IForm>({
    resolver: zodResolver(formSchema(dictionary)),
    defaultValues: {
      email: "",
      password: "",
    },
  })

  const [isDesactivate2FAModalOpen, setDesactivate2FAModalOpen] = React.useState(false)
  const [otpPromiseResolve, setOtpPromiseResolve] = React.useState<(otp: string | null) => void>()

  const getOtpCode = () => {
    return new Promise<string>((resolve) => {
      setOtpPromiseResolve(() => resolve)
      setDesactivate2FAModalOpen(true)
    })
  }

  async function onSubmit(data: IForm) {
    setIsLoading(true)
    try {
      const isPushingRoute = await handleSignIn({ data, callbackUrl, dictionary, getOtpCode })
      if (!isPushingRoute) setIsLoading(false)
    } catch {
      setIsLoading(false)
    }
  }

  return (
    <>
      <form onSubmit={form.handleSubmit(onSubmit)} {...props} className={cn("grid gap-6", props.className)}>
        <span className="text-foreground">Email ou Téléphone</span>
        <FormField
          form={form}
          name="email"
          label={dictionary.email}
          type="email"
          autoCapitalize="none"
          autoComplete="username"
          autoCorrect="off"
          isDisabled={isLoading}
        />
        <span className="text-foreground">Mot de passe</span>
        <FormField
          form={form}
          name="password"
          label={dictionary.password}
          type="password-eye-slash"
          autoComplete="current-password"
          autoCorrect="off"
          isDisabled={isLoading}
        />
        <Link className="ml-auto text-sm text-secondary" href={"/forgot-password"}>
          {dictionary.forgotPassword}
        </Link>
        <Button type="submit" isLoading={isLoading} color="primary" className="rounded-md">
          Se connecter
        </Button>
      </form>

      <TotpVerificationModal
        dictionary={dictionary}
        isOpen={isDesactivate2FAModalOpen}
        onOpenChange={(isOpen) => {
          if (!isOpen && otpPromiseResolve) {
            otpPromiseResolve(null)
          }
          setDesactivate2FAModalOpen(isOpen)
        }}
        onConfirm={(otp) => {
          if (otpPromiseResolve) {
            otpPromiseResolve(otp)
            setDesactivate2FAModalOpen(false)
          }
        }}
        title={dictionary.totp.enterCode}
        submitText={dictionary.confirm}
        closeText={dictionary.cancel}
        onlyPrompt
        curEmail={form.watch("email")}
      />
    </>
  )
}
