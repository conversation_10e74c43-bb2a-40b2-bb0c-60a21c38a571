"use client"
import React, { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react"
import Image from "next/image"

import { ChatInput } from "@/components/chatcomponents/chatinput"
import { ChatMessage } from "@/components/chatcomponents/chatmessage"
import { updateFolderModifiedStatus } from "@/components/chatcomponents/sidebar"
import { chunkAndEmbedDocument } from "@/lib/folder-utils"
import { getAllFolders } from "@/lib/folder-utils"
import { trpcClient } from "@/lib/trpc/client"
import type { FolderType } from "@/types/index-type"
import { type Message, useChat } from "@ai-sdk/react"
import { logger } from "@buildismart/lib"
type GenerateSummaryResponse = {
  summary: string
}

interface EnhancedMessage extends Message {
  fileData?: {
    name: string
    type?: string
    url?: string
    summary?: string
    shortSummary?: string
    isDirectory?: boolean
    nombrefichiers?: number
    texteextrait?: string
  }
}
interface MessageData {
  isUpdate?: boolean
  file?: {
    id: string
    name: string
    type?: string
    summary?: string
    shortSummary?: string
    texteextrait?: string
    isDirectory?: boolean
    nombrefichiers?: number
  }
}

interface ClientChatProps {
  initialFolder?: {
    id: string
    title: string
    projectId: string
    Ismodified: boolean
    texteextraitdossier: string
    nombrefichiers: number
    files: Array<{
      id?: string
      name: string
      type: string
      url?: string
      summary?: string
      textextraitfichier?: string
    }>
  } | null
}
const generateProjectSummary = async (projectId: string, projectSummary: string) => {
  try {
    const response = await fetch("/api/generate-project-summary", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ projectSummary }),
    })

    if (!response.ok) {
      throw new Error("Erreur lors de la génération du résumé")
    }

    const data = (await response.json()) as GenerateSummaryResponse
    return data.summary
  } catch (error) {
    logger.error("Erreur:", error)
    return projectSummary // Fallback au summary original en cas d'erreur
  }
}
const DocumentInfoSection = ({
  fileData,
  projectId
}: {
  fileData?: EnhancedMessage["fileData"]
  projectId?: string
}) => {
  const [projectStatus, setProjectStatus] = useState<{
    status: string
    message: string
    progress: number
    isComplete: boolean
  } | null>(null)

  useEffect(() => {
    if (projectId) {
      const fetchProjectStatus = async () => {
        try {
          const status = await trpcClient.folders.getProjectStatus.query({ projectId })
          setProjectStatus(status)
        } catch (error) {
          logger.error("Error fetching project status:", error)
          setProjectStatus({
            status: "ERROR",
            message: "Erreur lors de la récupération du statut",
            progress: 0,
            isComplete: false,
          })
        }
      }

      fetchProjectStatus()

      // Poll for status updates every 5 seconds if processing
      const interval = setInterval(() => {
        if (projectStatus?.status === "PROCESSING") {
          fetchProjectStatus()
        }
      }, 5000)

      return () => clearInterval(interval)
    }
  }, [projectId, projectStatus?.status])

  if (!fileData) return null

  return (
    <div className="group relative mb-4 rounded-lg border border-gray-200 bg-white p-3">
      <div className="flex flex-col gap-2">
        <p className="text-lg font-bold text-black">
          {fileData.isDirectory ? "Information du Projet" : "Information du Document"}
        </p>
        <p className="text-black">
          <span className="font-bold">{fileData.isDirectory ? "Nom du projet :" : "Nom du document :"}</span>{" "}
          {fileData.name}
        </p>

        {fileData.isDirectory && (
          <p className="text-black">
            <span className="font-bold">Nombre de fichiers :</span> {fileData.nombrefichiers}
          </p>
        )}
        {fileData.isDirectory && fileData.shortSummary?.trim() && (
          <p className="text-black">
            <span className="font-bold">Résumé :</span> {fileData.shortSummary}
          </p>
        )}

        <div className="flex justify-between text-sm text-black">
          <span>
            <span className="font-bold">Date de création:</span> {new Date().toLocaleDateString()}
          </span>
          <span>
            <span className="font-bold">Statut:</span>{" "}
            <span className={`${projectStatus?.status === "COMPLETE" ? "text-green-600" :
              projectStatus?.status === "PROCESSING" ? "text-blue-600" :
                projectStatus?.status === "ERROR" ? "text-red-600" :
                  projectStatus?.status === "NO_DOCUMENTS" ? "text-gray-500" :
                    "text-yellow-600"
              }`}>
              {projectStatus?.message || "Chargement..."}
            </span>
          </span>
        </div>

        {projectStatus?.status === "PROCESSING" && (
          <div className="mt-2">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${projectStatus.progress}%` }}
              ></div>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Progression: {projectStatus.progress}%
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
export default function ClientChat({ initialFolder }: ClientChatProps) {
  const sessionId = initialFolder?.id || "default"
  const { messages, input, handleInputChange, append, isLoading } = useChat({
    api: "/api/chat",
    id: sessionId,
    body: {
      projectId: initialFolder?.id,
    },
  })

  const [extractedContent, _i] = useState<string | null>(null)
  const [hasSentInitialData, setHasSentInitialData] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [_, setFolders] = useState<FolderType[]>([])
  const [shortSummary, setShortSummary] = useState<string | null>(null)
  const i = useRef(true)
  const handleProcess = useCallback(async () => {
    setIsProcessing(true)

    try {
      if (initialFolder) {
        await handleProcessFolder(initialFolder)
      }
    } finally {
      setIsProcessing(false)
    }
  }, [initialFolder])
  useEffect(() => {
    const initialDataSentKey = initialFolder?.id
    const hasAlreadySent = initialDataSentKey ? localStorage.getItem(`sent_${initialDataSentKey}`) : null

    if (initialFolder && !hasSentInitialData && !hasAlreadySent) {
      const processData = async () => {
        await handleProcess()
        setHasSentInitialData(true)
        if (initialDataSentKey) {
          localStorage.setItem(`sent_${initialDataSentKey}`, "true")
        }
        localStorage.setItem("hasVisitedOnce", "true")
        const folders = await getAllFolders()
        await updateFolderModifiedStatus(initialFolder?.id, true, folders, setFolders)
      }
      processData()
    }
  }, [initialFolder, handleProcess, hasSentInitialData])

  const [isPageLoading, setIsPageLoading] = useState(true)
  useEffect(() => {
    // Simule un délai de chargement ou attends que tout soit prêt
    const timeout = setTimeout(() => {
      setIsPageLoading(false)
    }, 1500) // 1.5s ou ajuste selon ton besoin

    return () => clearTimeout(timeout)
  }, [])

  const handleProcessFolder = async (folder: {
    id: string
    title: string
    nombrefichiers: number
    summary?: string
    projectId?: string
    texteextraitdossier: string
    files: Array<{
      id?: string
      name: string
      type: string
      url?: string
      summary?: string
      textextraitfichier?: string
    }>
  }) => {
    logger.log("⚠️ handleProcessFolder appelé !")
    setIsProcessing(true)
    try {
      // Traitement des fichiers
      await Promise.all(folder.files.map((file) => chunkAndEmbedDocument(file.id!)))

      // Mise à jour du résumé
      const generatedShortSummary = await generateProjectSummary(folder.id, folder.texteextraitdossier)
      setShortSummary(generatedShortSummary)
      // Ajout de la réponse assistant dans le chat
      logger.log("i.current")
      logger.log(i.current)

      i.current = false
    } catch (error) {
      logger.error("Erreur lors du traitement du dossier:", error)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleSubmit = async (userInput: string) => {
    if (userInput.trim()) {
      // Sauvegarder le contenu avant de vider l'input
      const contentToSend = userInput
      const hiddenContent = extractedContent || ""

      // Vider l'input immédiatement
      handleInputChange({ target: { value: "" } } as React.ChangeEvent<HTMLTextAreaElement>)

      // Envoyer le message ensuite
      await append({
        content: contentToSend,
        role: "user",
        data: {
          hiddenContent: hiddenContent,
        },
      })
    }
  }
  const filteredMessages = messages.filter(
    (msg) => (msg.role === "user" || msg.role === "assistant") && "content" in msg
  )

  const enhancedMessages: EnhancedMessage[] = filteredMessages.map((msg, index) => {
    const data = msg.data as MessageData

    if ((index > 0 && msg.role === "user") || data?.isUpdate) {
      return msg
    }

    const fileInfo = data?.file

    if (fileInfo?.id) {
      return {
        ...msg,
        fileData: {
          name: fileInfo.name,
          type: fileInfo.type,
          url: fileInfo.id.startsWith("http") ? fileInfo.id : `/api/files/${fileInfo.id}`,
          summary: fileInfo.summary,
          shortSummary: fileInfo.shortSummary,
          texteextrait: fileInfo.texteextrait,
          isDirectory: fileInfo.isDirectory,
          nombrefichiers: fileInfo.nombrefichiers,
        },
      }
    }
    return msg
  })

  const isChatEmpty = enhancedMessages.length === 0

  if (isPageLoading) {
    return (
      <div className="flex h-screen items-center justify-center bg-white">
        <span className="animate-spin text-2xl text-gray-600">⏳</span>
      </div>
    )
  }
  return (
    <div className="flex h-screen items-center justify-center bg-gray-100 px-2">
      <div className="flex size-full max-w-2xl flex-col">
        <div className="flex-1 overflow-y-auto px-4 py-2 pb-24 scrollbar-hide">
          {/* Afficher les informations du document/dossier en haut, même sans messages */}
          {!isProcessing && initialFolder && (
            <DocumentInfoSection
              fileData={{
                name: initialFolder?.title || "",
                shortSummary: shortSummary ?? undefined,
                isDirectory: !!initialFolder,
                nombrefichiers: initialFolder?.nombrefichiers || 0,
                texteextrait: initialFolder?.texteextraitdossier || "",
              }}
              projectId={initialFolder?.id}
            />
          )}

          {isChatEmpty ? (
            <div className="flex h-full flex-1 flex-col items-center justify-center gap-4 text-center">
              {isProcessing && ( // Ajout de cette condition pour afficher seulement pendant le traitement
                <>
                  <Image src={`/robot.png?${Date.now()}`} alt="Robot" width={50} height={50} />
                  <div className="space-y-[0.5px]">
                    <h2 className="text-xl font-bold text-black">
                      Bienvenue sur votre assistant d&rsquo;analyse de DCE !
                    </h2>
                    <p className="text-sm text-gray-500">
                      <span className="text-blue-500">Analyse en cours...</span>
                    </p>
                  </div>
                </>
              )}
              {!isProcessing && ( // Message affiché quand le traitement est terminé et le chat est vide
                <div className="space-y-[0.5px]">
                  <Image src={`/robot.png?${Date.now()}`} alt="Robot" width={50} height={50} />
                  <h2 className="text-xl font-bold text-black">
                    Bienvenue sur votre assistant d&rsquo;analyse de DCE !
                  </h2>
                  <p className="text-sm text-gray-500">
                    Vous pouvez maintenant poser vos questions sur les documents analysés.
                  </p>
                </div>
              )}
            </div>
          ) : (
            <div className="flex flex-col items-center gap-4 pb-4">
              {enhancedMessages.map((msg, index) => (
                <div key={index} className="flex w-full max-w-3xl flex-col gap-2">
                  <ChatMessage role={msg.role as "user" | "assistant"} content={msg.content as string} />
                </div>
              ))}
              {isLoading && <ChatMessage role="assistant" content="⏳ Rédaction de la réponse..." />}
            </div>
          )}
        </div>

        <div className="sticky bottom-0 flex justify-center border-t bg-gray-100 py-2">
          <ChatInput input={input} onInputChange={handleInputChange} onSubmit={() => handleSubmit(input)} />
        </div>
      </div>
    </div>
  )
}
