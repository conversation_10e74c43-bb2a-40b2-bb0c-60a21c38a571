"use client"

import { useEffect, useState } from "react"

import ClientChat from "@/app/[lang]/(protected)/chat/clientchat"
import { Sidebar } from "@/components/chatcomponents/sidebar"
import { getAllFolders, getFolderById } from "@/lib/folder-utils"
import type { FolderType } from "@/types/index-type"
import { logger } from "@buildismart/lib"
import { Spinner } from "@nextui-org/react"
type FolderDataType = Awaited<ReturnType<typeof getFolderById>> & {
  children?: FolderDataType[]
}

interface ClientDossierChatPageProps {
  id: string
}
function deserializeFolder(folder: FolderDataType & { updatedAt?: Date }): FolderType {
  return {
    ...folder,
    createdAt: new Date(folder.createdAt),
    updatedAt: folder.updatedAt ? new Date(folder.updatedAt) : undefined,
    files: folder.files || [],
    children: folder.children?.map(deserializeFolder),
  }
}

export default function ClientDossierChatPage({ id }: ClientDossierChatPageProps) {
  const [folder, setFolder] = useState<FolderType | null>(null)
  const [folders, setFolders] = useState<FolderType[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function loadData() {
      try {
        const folderData = await getFolderById(id)
        setFolder(deserializeFolder(folderData))
        logger.log(folderData)
        const allFoldersData = await getAllFolders()
        setFolders(allFoldersData)
      } catch (error) {
        console.error("Error loading data:", error)
      } finally {
        setLoading(false)
      }
    }
    loadData()
  }, [id])

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Spinner size="lg" label="Chargement de la page..." />
      </div>
    )
  }

  if (!folder) {
    return (
      <div className="flex h-screen flex-col items-center justify-center gap-4">
        <h1 className="text-2xl font-bold">Dossier non trouvé</h1>
        <p>Le dossier avec l&apos;ID {id} n&apos;existe pas</p>
      </div>
    )
  }

  return (
    <div className="flex h-screen">
      <Sidebar folders={folders} />
      <div className="flex-1">
        <ClientChat
          initialFolder={{
            id: folder.id,
            projectId: folder.id,
            title: folder.title,
            nombrefichiers: folder.nombrefichiers ?? 0,
            Ismodified: folder.Ismodified ?? false,
            texteextraitdossier: folder.texteextraitdossier || "",
            files: folder.files || [], // attention à bien définir files dans FolderType
          }}
        />
      </div>
    </div>
  )
}
