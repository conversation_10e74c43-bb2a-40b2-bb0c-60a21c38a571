import { TDictionary } from "@/lib/langs"
import { <PERSON> } from "@nextui-org/link"

import { PrivacyAcceptanceDr } from "./privacy-acceptance.dr"

export default function PrivacyAcceptance({ dictionary }: { dictionary: TDictionary<typeof PrivacyAcceptanceDr> }) {
  return (
    <p className="text-muted-foreground px-8 text-center text-sm">
      {dictionary.auth.clickingAggreement}{" "}
      <Link href="/terms" className="inline text-sm underline underline-offset-4 hover:text-primary">
        {dictionary.auth.termsOfService}
      </Link>{" "}
      {dictionary.and}{" "}
      <Link href="/privacy" className="inline text-sm underline underline-offset-4 hover:text-primary">
        {dictionary.auth.privacyPolicy}
      </Link>
      .
    </p>
  )
}
