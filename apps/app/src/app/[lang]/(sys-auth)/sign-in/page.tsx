import Image from "next/image"
import Link from "next/link"

import { LoginUserAuthForm } from "@/components/auth/login-user-auth-form"
import { LoginUserAuthFormDr } from "@/components/auth/login-user-auth-form.dr"
import { auth } from "@/lib/auth"
import { Locale } from "@/lib/i18n-config"
import { getDictionary } from "@/lib/langs"
import { dictionaryRequirements } from "@/lib/utils/dictionary"
import { Button } from "@nextui-org/button"

export default async function SignInPage({
  searchParams,
  params: { lang },
}: {
  searchParams: { [key: string]: string | string[] | undefined }
  params: {
    lang: Locale
  }
}) {
  const dictionary = await getDictionary(
    lang,
    dictionaryRequirements(
      {
        signInPage: {
          loginToYourAccount: true,
          enterDetails: true,
        },
        toSignUp: true,
      },
      LoginUserAuthFormDr
    )
  )

  await auth()

  return (
    <main className="grid min-h-screen grid-cols-1 overflow-x-hidden lg:grid-cols-2">
      {/* Colonne gauche - visible uniquement en large écran */}
      <div className="relative hidden flex-col items-center justify-center bg-primary p-6 text-content1 lg:flex">
        <div
          className="absolute flex items-center text-xl font-bold text-content1"
          style={{ top: "29px", left: "100px" }}
        >
          <Image src="/logo.png" alt="BUILDISMART" width={200} height={36} />
        </div>

        <div className="relative mx-auto mb-4 size-[547px] max-w-5xl">
          <Image src="/Polygon.png" alt="Polygon" width={432} height={432} className="ml-16 mt-44" />
          <Image src="/architecte.png" alt="Ingénieure" fill style={{ objectFit: "contain" }} priority />
        </div>

        <div className="max-w-xl text-center">
          <h2 className="mb-2 text-xl font-semibold">L&rsquo;IA&nbsp; analyse vous accompagne</h2>
          <p className="text-base">
            Décryptez vos appels d&rsquo;offres&nbsp; et gagnez du temps avec notre assistant IA.
          </p>
        </div>
      </div>

      <div className="relative flex w-full flex-col items-center justify-center bg-content1 px-6 py-12 text-content3 lg:px-16">
        <div className="absolute right-6 top-6 lg:right-[100px] lg:top-[29px]">
          <Button
            as={Link}
            href="/"
            color="secondary"
            variant="light"
            className="flex h-[36px] w-[180px] items-center gap-2 rounded-[10px] bg-couleur-tertiaire px-3 py-1 text-xs lg:h-[48px] lg:w-[294px] lg:text-sm"
          >
            <Image src="/diamond.png" alt="diamant" width={20} height={20} className="lg:size-7" />
            Passer au plan supérieur
          </Button>
        </div>

        <div className="mt-20 w-full max-w-full lg:mt-0 lg:max-w-xl">
          <div className="mb-[101px] h-[74px] w-full">
            <h1 className="mb-2 text-center text-3xl font-bold text-foreground">Connexion</h1>
            <p className="mb-8 text-center text-base text-gray500">Connectez-vous pour accéder à votre compte</p>
          </div>

          {/* Formulaire */}
          <LoginUserAuthForm dictionary={dictionary} searchParams={searchParams} />
        </div>
      </div>
    </main>
  )
}
