import React from "react"
import Image from "next/image"
import Link from "next/link"

import { RegisterUserAuthForm } from "@/components/auth/register-user-auth-form"
import { RegisterUserAuthFormDr } from "@/components/auth/register-user-auth-form.dr"
import { authRoutes } from "@/constants/auth"
import { auth } from "@/lib/auth"
import { Locale } from "@/lib/i18n-config"
import { getDictionary } from "@/lib/langs"
import { cn } from "@/lib/utils"
import { dictionaryRequirements } from "@/lib/utils/dictionary"
import { Button } from "@nextui-org/button"

import PrivacyAcceptance from "../privacy-acceptance"
import { PrivacyAcceptanceDr } from "../privacy-acceptance.dr"
//import AuthProviders from "../providers"
import { AuthProvidersDr } from "../providers.dr"

export default async function SignUpPage({
  searchParams,
  params: { lang },
}: {
  searchParams: { [key: string]: string | string[] | undefined }
  params: {
    lang: Locale
  }
}) {
  const dictionary = await getDictionary(
    lang,
    dictionaryRequirements(
      {
        login: true,
        signUpPage: {
          createAnAccount: true,
          enterEmail: true,
        },
        auth: {
          orContinueWith: true,
        },
      },
      AuthProvidersDr,
      PrivacyAcceptanceDr,
      RegisterUserAuthFormDr
    )
  )
  const _session = await auth()

  return (
    <main className="grid min-h-screen w-full lg:grid-cols-2">
      {/* Bouton vers login */}
      <Button
        as={Link}
        href={authRoutes.signIn[0]}
        className={cn("absolute right-4 top-4 z-10 md:right-8 md:top-8")}
        variant="ghost"
      >
        {dictionary.login}
      </Button>

      <div className="relative hidden flex-col items-center justify-center bg-primary p-6 text-content1 lg:flex">
        <div
          className="absolute flex items-center text-xl font-bold text-content1"
          style={{ top: "29px", left: "100px" }}
        >
          <Image src="/logo.png" alt="BUILDISMART" width={200} height={36} />
        </div>

        <div className="relative mx-auto mb-4 size-[547px] max-w-5xl">
          <Image src="/Polygon.png" alt="Polygon" width={432} height={432} className="ml-16 mt-44" />
          <Image src="/architecte.png" alt="Ingénieure" fill style={{ objectFit: "contain" }} priority />
        </div>

        <div className="max-w-xl text-center">
          <h2 className="mb-2 text-xl font-semibold">L&rsquo;IA&nbsp; analyse vous accompagne</h2>
          <p className="text-base">
            Décryptez vos appels d&rsquo;offres&nbsp; et gagnez du temps avec notre assistant IA.
          </p>
        </div>
      </div>

      {/* Colonne droite (formulaire) */}
      <div className="flex items-center justify-center px-4 py-12 sm:px-6 lg:px-8">
        <div className="w-full max-w-md space-y-6">
          <div className="text-center">
            <h1 className="text-2xl font-semibold tracking-tight">{dictionary.signUpPage.createAnAccount}</h1>
            <p className="text-muted-foreground mt-2 text-sm">{dictionary.signUpPage.enterEmail}</p>
          </div>

          <div className="grid gap-6">
            <RegisterUserAuthForm dictionary={dictionary} isMinimized searchParams={searchParams} locale={lang} />
          </div>

          <PrivacyAcceptance dictionary={dictionary} />
        </div>
      </div>
    </main>
  )
}
