import React from "react"
import { Poppins } from "next/font/google"
import Image from "next/image"

import AccordionItem from "@/components/tarif/accordionitem"
import Footer from "@/components/tarif/footer"
import NavPricing from "@/components/tarif/navpricing"
import PricingHeader from "@/components/tarif/pricingheader"
import { Locale } from "@/lib/i18n-config"
import { getDictionary } from "@/lib/langs"
import { dictionaryRequirements } from "@/lib/utils/dictionary"

const poppins = Poppins({
  subsets: ["latin"],

  weight: ["400", "500", "600", "700"], // Ajoutez tous les poids nécessaires

  variable: "--font-poppins",

  display: "swap", // Important pour le chargement
})

export default async function Home({ params: { lang } }: { params: { lang: Locale } }) {
  const _dictionary = await getDictionary(
    lang,

    dictionaryRequirements({
      homePage: { title: true },

      profile: true,
    })
  )

  return (
    <div className={`${poppins.variable} font-main`}>
      <main className="flex min-h-screen flex-col overflow-x-hidden font-main">
        <div className="w-full px-12 sm:px-8 md:px-8">
          <NavPricing />
        </div>

        <div className="relative flex w-full items-center justify-center px-4 py-16 sm:py-24 md:py-32">
          <div
            className="absolute z-0 size-[250px] rounded-full opacity-30 blur-3xl sm:size-[350px] md:size-[450px] lg:size-[550px] xl:size-[650px]"
            style={{
              background: "radial-gradient(circle, #fc90ce 0%, #f0c3cb 40%, #ffffff 80%)",

              left: "-5rem",

              top: "-5rem",
            }}
          />

          <div
            className="absolute z-0 size-[250px] rounded-full opacity-30 blur-3xl sm:size-[350px] md:size-[450px] lg:size-[550px] xl:size-[650px]"
            style={{
              background: "radial-gradient(circle, #90b4fc 0%, #c3dcf0 40%, #ffffff 80%)",

              right: "-5rem",

              top: "-5rem",
            }}
          />

          <div className="relative z-10 max-w-7xl px-4 text-center text-foreground">
            <h1 className="mb-4 text-3xl font-bold md:text-4xl lg:text-5xl">
              Boostez votre réactivité sur les appels d&rsquo;offres&nbsp;!
            </h1>

            <p className="text-lg text-default-500 md:text-xl">
              Choisissez le plan adapté et laissez l&rsquo;IA répondre à vos requêtes
            </p>
          </div>
        </div>

        <div className="w-full p-4">
          <PricingHeader />
        </div>

        {/* Section bénéfices */}

        <div className="mx-auto mt-36 w-full bg-primary-100 p-10 text-foreground">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            <div className="flex flex-col items-center gap-4 text-center md:flex-row md:items-start md:text-start">
              <div className="rounded-full p-3">
                <Image src="/temps.png" alt="Horloges" width={32} height={32} />
              </div>

              <div>
                <h1 className="text-xl font-bold">Gagner du temps</h1>

                <p className="text-default-600">L&rsquo;IA vous guide sur les réponses</p>
              </div>
            </div>

            <div className="flex flex-col items-center gap-4 text-center md:flex-row md:items-start md:text-start">
              <div className="rounded-full p-3">
                <Image src="/star.png" alt="star" width={32} height={32} />
              </div>

              <div>
                <h1 className="text-xl font-bold">Optimisez vos chances de gagner</h1>

                <p className="text-default-600">Nos analyses sont basées sur des standards reconnus</p>
              </div>
            </div>

            <div className="flex flex-col items-center gap-4 text-center md:flex-row md:items-start md:text-start">
              <div className="rounded-full p-3">
                <Image src="/cerveau.png" alt="cerveau" width={32} height={32} />
              </div>

              <div>
                <h1 className="text-xl font-bold">Faites confiance à l&rsquo;IA</h1>

                <p className="text-default-600">Technologie innovante et sécurisée.</p>
              </div>
            </div>
          </div>
        </div>

        {/* FAQ */}

        <div className="mt-36 grid grid-cols-1 gap-8 px-8 text-foreground md:grid-cols-2">
          <div className="text-start md:ml-[100px]">
            <h1 className="mb-4 text-4xl font-bold">
              Des questions&nbsp;? Nous avons les <br /> réponses !
            </h1>

            <p className="text-lg text-default-600">
              Tout ce que vous devez savoir avant de souscrire à un <br /> abonnement
            </p>
          </div>

          <div>
            <div className="rounded-lg p-4 transition-all duration-300 hover:bg-gray-100 hover:shadow-sm">
              <AccordionItem
                title="Comment fonctionne votre l'IA pour analyser mes documents&nbsp;?"
                content="Notre plateforme analyse les DCE automatiquement grâce à une IA avancée. Vous téléchargez vos documents et recevez une analyse rapide."
              />
            </div>

            <div className="rounded-lg p-4 transition-all duration-300 hover:bg-gray-100 hover:shadow-sm">
              <AccordionItem
                title="Y a-t-il une période d'essai gratuite&nbsp;?"
                content="Nous offrons une période d'essai de 3 jours pour vous permettre de prendre connaissance de nos services."
              />
            </div>

            <div className="rounded-lg p-4 transition-all duration-300 hover:bg-gray-100 hover:shadow-sm">
              <AccordionItem
                title="Quels types de documents puis-je importer pour analyse&nbsp;?"
                content="Vous pouvez analyser tous types de documents, y compris les PDF, Word et Excel. Nous acceptons également les fichiers compressés."
              />
            </div>

            <div className="rounded-lg p-4 transition-all duration-300 hover:bg-gray-100 hover:shadow-sm">
              <AccordionItem
                title="Comment mes données sont-elles sécurisées&nbsp;?"
                content="Vos données sont sécurisées grâce à un chiffrement avancé et à des protocoles de sécurité stricts. Nous ne partageons pas vos informations avec des tiers."
              />
            </div>
          </div>
        </div>

        <div className="mt-20 w-full">
          <Footer />
        </div>
      </main>
    </div>
  )
}
