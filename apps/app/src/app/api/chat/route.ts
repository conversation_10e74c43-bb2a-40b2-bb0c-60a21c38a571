import { NextRequest } from "next/server"
import { Message, streamText, tool } from "ai"
import { z } from "zod"

import { semanticSearch, semanticSearchInDocumentChunks } from "@/lib/ai/embeddings"
import { prisma } from "@/lib/prisma"
import { openai } from "@ai-sdk/openai"
import { logger } from "@buildismart/lib"

// Types for search results
interface DocumentResult {
  originalFileName: string
  content: string
  similarity: number
}

interface ChunkResult {
  originalFileName: string
  content: string
  similarity: number
  pageNumber?: number
}

export const maxDuration = 150

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const { messages, projectId, ...otherParams } = body as {
      messages: Message[]
      projectId: string
      [key: string]: unknown
    }

    if (!messages || !projectId) {
      return new Response("Requête invalide: messages ou projectId manquant", { status: 400 })
    }

    logger.log("Chat request received", {
      projectId,
      messagesCount: messages.length,
      otherParams: Object.keys(otherParams),
    })

    // Validate project exists and get essential context only
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      select: {
        id: true,
        name: true,
        projectSummary: true,
        createdAt: true,
        allProjectDocuments: {
          where: { status: "READY" },
          select: {
            id: true,
            originalFileName: true,
            mimeType: true,
            createdAt: true,
            fileSummary: true,
          },
          orderBy: { createdAt: "asc" },
        },
      },
    })

    if (!project) {
      return new Response("Projet non trouvé", { status: 404 })
    }

    // Build lightweight initial context (NO automatic search)
    const initialContext = `**Projet:** ${project.name}
${project.projectSummary ? `**Résumé:** ${project.projectSummary}` : ""}

**Documents disponibles (${project.allProjectDocuments.length} traités):**
${
  project.allProjectDocuments.length > 0
    ? project.allProjectDocuments
        .map((doc) => {
          const fileType = doc.mimeType.includes("pdf")
            ? "📄 PDF"
            : doc.mimeType.includes("word")
              ? "📝 Word"
              : doc.mimeType.includes("image")
                ? "🖼️ Image"
                : "📁 Fichier"
          return `• ${fileType} ${doc.originalFileName}${doc.fileSummary ? ` - ${doc.fileSummary.substring(0, 100)}...` : ""}`
        })
        .join("\n")
    : "Aucun document traité disponible"
}

**Instructions:** Utilisez les outils de recherche pour accéder au contenu spécifique des documents selon les besoins de l'utilisateur.`

    // Enhanced tools for construction-specific queries
    const deepSearchTool = tool({
      description: "🔍 Recherche approfondie dans les extraits détaillés des documents de construction",
      parameters: z.object({
        query: z.string().describe("La question technique ou spécifique à rechercher"),
        focus: z
          .enum(["technique", "prix", "planning", "normes", "materiaux"])
          .optional()
          .describe("Type de focus pour la recherche"),
      }),
      execute: async ({ query, focus }) => {
        logger.log("Deep search tool called", { query, focus, projectId })

        const enhancedQuery = focus
          ? `${query} ${
              focus === "technique"
                ? "spécifications techniques"
                : focus === "prix"
                  ? "coûts prix devis"
                  : focus === "planning"
                    ? "délais planning échéances"
                    : focus === "normes"
                      ? "normes réglementation DTU"
                      : "matériaux fournitures"
            }`
          : query

        const {
          chunks = [],
          totalChunks,
          totalDocuments,
        } = await semanticSearchInDocumentChunks(projectId, enhancedQuery, 12)

        if (Array.isArray(chunks) && chunks.length > 0) {
          const formattedChunks = (chunks as ChunkResult[])
            .map(
              (chunk) =>
                `📄 **${chunk.originalFileName}** (page ${chunk.pageNumber || "N/A"}, similarité: ${(chunk.similarity * 100).toFixed(1)}%)\n${chunk.content}`
            )
            .join("\n\n")

          return {
            status: `✅ Trouvé ${chunks.length} extraits pertinents sur ${totalChunks} dans ${totalDocuments} documents`,
            context: formattedChunks,
            metadata: { chunksFound: chunks.length, totalChunks, totalDocuments, focus },
          }
        }

        return {
          status: "❌ Aucun extrait détaillé trouvé pour cette recherche",
          context: "Aucun contenu spécifique trouvé dans les documents.",
          metadata: { chunksFound: 0, totalChunks, totalDocuments, focus },
        }
      },
    })

    const projectAnalysisTool = tool({
      description: "📊 Analyse globale du projet de construction avec synthèse des documents",
      parameters: z.object({
        analysisType: z
          .enum(["overview", "technical", "financial", "regulatory"])
          .describe("Type d'analyse à effectuer"),
      }),
      execute: async ({ analysisType }) => {
        logger.log("Project analysis tool called", { analysisType, projectId })

        try {
          const documents = await prisma.projectDocument.findMany({
            where: { projectId, status: "READY" },
            select: {
              originalFileName: true,
              fileSummary: true,
              mimeType: true,
              createdAt: true,
            },
            orderBy: { createdAt: "asc" },
          })

          if (documents.length === 0) {
            return {
              status: "❌ Aucun document analysé disponible",
              analysis: "Le projet ne contient pas encore de documents traités.",
              metadata: { documentsCount: 0, analysisType },
            }
          }

          // Analysis type is used in the response formatting

          const documentsSummary = documents
            .map((doc) => `• ${doc.originalFileName}: ${doc.fileSummary || "Pas de résumé"}`)
            .join("\n")

          return {
            status: `✅ Analyse ${analysisType} générée pour ${documents.length} documents`,
            analysis: `**Analyse ${analysisType} du projet "${project.name}":**\n\n${documentsSummary}`,
            metadata: {
              documentsCount: documents.length,
              analysisType,
              documentTypes: [...new Set(documents.map((d) => d.mimeType))],
            },
          }
        } catch (error) {
          logger.error("Project analysis failed", { error, projectId, analysisType })
          return {
            status: "❌ Erreur lors de l'analyse du projet",
            analysis: "Une erreur est survenue pendant l'analyse.",
            metadata: { error: true, analysisType },
          }
        }
      },
    })

    const documentSearchTool = tool({
      description: "📋 Recherche ciblée dans des types de documents spécifiques (CCTP, DPGF, plans, etc.)",
      parameters: z.object({
        query: z.string().describe("Terme ou concept à rechercher"),
        documentType: z
          .enum(["CCTP", "DPGF", "plans", "devis", "tous"])
          .optional()
          .describe("Type de document à cibler"),
      }),
      execute: async ({ query, documentType }) => {
        logger.log("Document search tool called", { query, documentType, projectId })

        const typeFilter = documentType && documentType !== "tous" ? documentType.toLowerCase() : null

        const { documents = [], documentsCount } = await semanticSearch(query, projectId, 8)

        let filteredDocuments = Array.isArray(documents) ? documents : []

        if (typeFilter) {
          filteredDocuments = (filteredDocuments as DocumentResult[]).filter(
            (doc) =>
              doc.originalFileName.toLowerCase().includes(typeFilter) ||
              (doc.content && doc.content.toLowerCase().includes(typeFilter))
          )
        }

        if (filteredDocuments.length > 0) {
          const formattedDocs = (filteredDocuments as DocumentResult[])
            .map(
              (doc) =>
                `📄 **${doc.originalFileName}** (similarité: ${(doc.similarity * 100).toFixed(1)}%)\n${doc.content}`
            )
            .join("\n\n")

          return {
            status: `✅ Trouvé ${filteredDocuments.length} documents${typeFilter ? ` de type ${documentType}` : ""} sur ${documentsCount}`,
            documents: formattedDocs,
            metadata: {
              documentsFound: filteredDocuments.length,
              totalDocuments: documentsCount,
              documentType: documentType || "tous",
            },
          }
        }

        return {
          status: `❌ Aucun document${typeFilter ? ` de type ${documentType}` : ""} trouvé pour "${query}"`,
          documents: "Aucun document correspondant trouvé.",
          metadata: {
            documentsFound: 0,
            totalDocuments: documentsCount,
            documentType: documentType || "tous",
          },
        }
      },
    })

    // Enhanced system prompt for construction expertise
    const systemPrompt = `Tu es BuildiSmart, un assistant IA expert en construction et BTP (Bâtiment et Travaux Publics).

**Ton rôle :** Analyser les documents de consultation d'entreprise (DCE) et aider les professionnels du BTP à comprendre les projets de construction.

**Tes capacités :**
- 🔍 Recherche approfondie dans les documents techniques
- 📊 Analyse de projets de construction
- 📋 Recherche ciblée par type de document
- 💡 Expertise en normes, réglementations et techniques BTP

**Instructions importantes :**
1. Base-toi UNIQUEMENT sur le contexte fourni et les outils disponibles
2. Si l'information n'est pas suffisante, utilise les outils pour chercher plus de détails
3. Indique toujours tes sources (nom du document, page si disponible)
4. Utilise un langage technique approprié au secteur BTP
5. Signale quand tu utilises un outil avec un message informatif

**Outils disponibles :**
- 🔍 **Recherche approfondie** : Pour les détails techniques spécifiques
- 📊 **Analyse de projet** : Pour une vue d'ensemble ou analyse thématique
- 📋 **Recherche par type** : Pour cibler CCTP, DPGF, plans, devis, etc.

**Projet actuel :** ${project.name}
**Documents disponibles :** ${project.allProjectDocuments.length} documents traités

**Contexte initial :**
${initialContext}

Réponds de manière précise et professionnelle en utilisant les outils si nécessaire.`

    const result = await streamText({
      model: openai("gpt-4o"),
      system: systemPrompt,
      messages,
      tools: {
        deepSearchTool,
        projectAnalysisTool,
        documentSearchTool,
      },
      maxSteps: 5,
      temperature: 0.1, // More deterministic for technical content
    })

    logger.log("Chat response generated", {
      projectId,
      messagesCount: messages.length,
      documentsAvailable: project.allProjectDocuments.length,
    })

    return result.toDataStreamResponse()
  } catch (error) {
    logger.error("Chat request failed", { error })
    return new Response(
      `Erreur lors du traitement de la requête: ${error instanceof Error ? error.message : "Erreur inconnue"}`,
      { status: 500 }
    )
  }
}
