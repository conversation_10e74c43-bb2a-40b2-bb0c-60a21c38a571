import { NextResponse } from "next/server"
import { OpenAI } from "openai"

import { env } from "@/lib/env"
import { logger } from "@buildismart/lib"

export const runtime = "nodejs"
export const maxDuration = 300

const openai = new OpenAI({
  apiKey: env.OPENAI_API_KEY,
})

const errorMessages = {
  noContent: "Aucun contenu de projet fourni",
  invalidContent: "Le contenu du projet doit être une chaîne non vide",
  summaryUnavailable: "Résumé de projet indisponible",
}

export async function POST(req: Request) {
  try {
    const { projectSummary } = (await req.json()) as { projectSummary: string }

    if (!projectSummary) {
      throw new Error(errorMessages.noContent)
    }

    if (typeof projectSummary !== "string" || projectSummary.trim().length === 0) {
      throw new Error(errorMessages.invalidContent)
    }

    const truncatedContent = projectSummary.substring(0, 15000)

    const chatResponse = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        {
          role: "system",
          content: `Tu es un expert en analyse de documents de projet. 
          Génère un résumé extrêmement concis en exactement 2 lignes:
          - Pas de saut de ligne supplémentaire
          - Maximum 120 caractères par ligne
          - Capture l'essentiel du projet
          - Style télégraphique sans verbes superflus`,
        },
        {
          role: "user",
          content: `Résume ce projet en 2 lignes strictes:\n\n${truncatedContent}`,
        },
      ],
      temperature: 0.3, // Pour plus de concision
      max_tokens: 200,
    })

    let summary = chatResponse.choices[0]?.message?.content || errorMessages.summaryUnavailable

    // Nettoyage du résultat
    summary = summary.replace(/\n+/g, "\n").trim()
    const lines = summary.split("\n")

    if (lines.length > 2) {
      summary = lines.slice(0, 2).join("\n")
    }

    return NextResponse.json({ summary })
  } catch (error: unknown) {
    logger.error("Erreur:", error)
    return NextResponse.json(
      { error: `Échec du traitement: ${error instanceof Error ? error.message : "Erreur inconnue"}` },
      { status: 500 }
    )
  }
}
