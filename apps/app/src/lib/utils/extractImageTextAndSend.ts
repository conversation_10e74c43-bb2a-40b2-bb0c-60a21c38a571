// utils/extractImageTextAndSend.ts
import { createWorker } from "tesseract.js"

import { logger } from "@buildismart/lib"

export async function extractTextFromImageAndSend(file: File) {
  const worker = await createWorker({
    logger: (m) => logger.log(m), // optionnel
  })

  try {
    await worker.load()
    await worker.loadLanguage("fra+eng")
    await worker.initialize("fra+eng")

    const { data } = await worker.recognize(file)
    const extractedText = data.text

    const formData = new FormData()
    const textFile = new File([extractedText], "ocr.txt", { type: "text/plain" })
    formData.append("file", textFile)

    const response = await fetch("/api/extract-text", {
      method: "POST",
      body: formData,
    })

    if (!response.ok) {
      throw new Error("Échec de l'envoi à l'API")
    }

    return await response.json()
  } finally {
    await worker.terminate()
  }
}
