// lib/utils/math.ts
export function cosineSimilarity(vecA: number[], vecB: number[]): number {
  if (vecA.length !== vecB.length) return 0

  const dotProduct = vecA.reduce((sum, a, i) => sum + a * vecB[i], 0)
  const magA = Math.sqrt(vecA.reduce((sum, a) => sum + a * a, 0))
  const magB = Math.sqrt(vecB.reduce((sum, b) => sum + b * b, 0))

  return magA && magB ? dotProduct / (magA * magB) : 0
}

export function normalizeVector(vec: number[]): number[] {
  const mag = Math.sqrt(vec.reduce((sum, x) => sum + x * x, 0))
  return mag ? vec.map((x) => x / mag) : vec
}
