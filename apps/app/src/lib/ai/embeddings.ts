import { embed, embedMany } from "ai"
import cuid from "cuid"

import { prisma } from "@/lib/prisma"
import { openai } from "@ai-sdk/openai"
import { logger } from "@buildismart/lib"

// Types for search results
interface DocumentSearchResult {
  id: string
  originalFileName: string
  content: string
  mimeType?: string
  status?: string
  similarity: number
  folderName?: string
}

interface ChunkSearchResult {
  id: string
  content: string
  pageNumber?: number
  metadata?: unknown
  documentId: string
  originalFileName: string
  mimeType?: string
  folderName?: string
  similarity: number
}
function cosineDistance(vecA: number[], vecB: number[]): number {
  if (vecA.length !== vecB.length) {
    throw new Error("Vectors must have the same length")
  }

  let dotProduct = 0
  let normA = 0
  let normB = 0

  for (let i = 0; i < vecA.length; i++) {
    dotProduct += vecA[i] * vecB[i]
    normA += vecA[i] * vecA[i]
    normB += vecB[i] * vecB[i]
  }

  normA = Math.sqrt(normA)
  normB = Math.sqrt(normB)

  if (normA === 0 || normB === 0) {
    return 1 // distance maximale si un vecteur est nul
  }

  return 1 - dotProduct / (normA * normB)
}
const embeddingModel = openai.embedding("text-embedding-3-small")

// Générer un embedding pour un texte
export async function generateEmbedding(text: string): Promise<number[]> {
  const { embedding } = await embed({
    model: embeddingModel,
    value: text,
  })
  return embedding
}

// Générer des embeddings pour plusieurs textes
export async function generateEmbeddings(texts: string[]): Promise<number[][]> {
  const { embeddings } = await embedMany({
    model: embeddingModel,
    values: texts,
  })
  return embeddings
}

// Mettre à jour l'embedding d'un document
export async function updateDocumentEmbedding(documentId: string) {
  const document = await prisma.projectDocument.findUnique({
    where: { id: documentId },
    include: { file: true },
  })
  logger.log("document?.fileSummary")
  logger.log(document?.fileSummary)
  if (!document?.fileSummary) return

  const embedding = await generateEmbedding(document.fileSummary)
  await prisma.$queryRaw`
        UPDATE "ProjectDocument"
        SET "fileSummaryEmbedding" = ${embedding}::vector
        WHERE id = ${documentId}
    `
  try {
    const vectorResult = await prisma.$queryRaw<{ dim: number }[]>`
            SELECT unnest("fileSummaryEmbedding"::real[]) as dim
            FROM "ProjectDocument"
            WHERE id = ${documentId}
            LIMIT 5
        `
    logger.log(
      "First 5 dimensions from DB:",
      vectorResult.map((r) => r.dim)
    )

    return { success: true, dimensions: embedding.length }
  } catch (error) {
    console.error("Verification failed:", error)
    return { success: false, error: "Verification failed" }
  }
}

// Mettre à jour l'embedding d'un projet
export async function updateProjectEmbedding(projectId: string) {
  const project = await prisma.project.findUnique({
    where: { id: projectId },
    include: { allProjectDocuments: true },
  })
  logger.log(project?.projectSummary)
  if (!project?.projectSummary) return
  const embedding = await generateEmbedding(project.projectSummary)

  await prisma.$queryRaw`
  UPDATE "Project"
  SET "projectSummaryEmbedding" = ${embedding}::vector
  WHERE id = ${projectId}
`

  try {
    const vectorResult = await prisma.$queryRaw<{ dim: number }[]>`
            SELECT unnest("projectSummaryEmbedding"::real[]) as dim
            FROM "Project"
            WHERE id = ${projectId}
            LIMIT 5
        `
    logger.log(
      "First 5 dimensions from DB:",
      vectorResult.map((r) => r.dim)
    )

    return { success: true, dimensions: embedding.length }
  } catch (error) {
    console.error("Verification failed:", error)
    return { success: false, error: "Verification failed" }
  }
}
// Note: This function is deprecated in favor of the improved chunking service
// Use chunkAndEmbedDocument from @/lib/ai/chunking instead
export async function chunkAndEmbedDocument(documentId: string) {
  // Import the improved chunking service
  const { chunkAndEmbedDocument: improvedChunkAndEmbed } = await import("@/lib/ai/chunking")

  try {
    const result = await improvedChunkAndEmbed(documentId)
    logger.log("Document chunking completed using improved service", {
      documentId,
      chunksCreated: result.totalChunks,
      processingTime: result.processingTime,
    })
    return result
  } catch (error) {
    logger.error("Improved chunking failed, falling back to basic chunking", { documentId, error })

    // Fallback to basic chunking
    const document = await prisma.projectDocument.findUnique({
      where: { id: documentId },
    })

    if (!document?.extractedText) {
      throw new Error("No extracted text found for document")
    }

    // Simple paragraph-based chunking as fallback
    const chunks = document.extractedText.split("\n\n").filter((chunk) => chunk.trim().length > 0)

    if (chunks.length === 0) {
      throw new Error("No chunks generated from document")
    }

    // Generate embeddings
    const embeddings = await generateEmbeddings(chunks)
    const now = new Date()

    // Delete existing chunks
    await prisma.documentChunk.deleteMany({
      where: { projectDocumentId: documentId },
    })

    // Insert new chunks using Prisma ORM for better type safety
    const chunkRecords = chunks.map((content, index) => ({
      id: cuid(),
      content,
      pageNumber: index + 1,
      metadata: {
        documentId,
        chunkIndex: index,
        createdAt: now.toISOString(),
        fallbackMethod: true,
      },
      projectDocumentId: documentId,
      createdAt: now,
      updatedAt: now,
    }))

    // Create chunks and set embeddings in a transaction
    await prisma.$transaction(async (tx) => {
      // First create all chunks without embeddings
      for (const chunk of chunkRecords) {
        await tx.documentChunk.create({
          data: chunk,
        })
      }

      // Then update embeddings using raw queries
      for (let i = 0; i < chunkRecords.length; i++) {
        await tx.$queryRaw`
          UPDATE "DocumentChunk"
          SET "embedding" = ${embeddings[i]}::vector
          WHERE id = ${chunkRecords[i].id}
        `
      }
    })

    // Update document status
    await prisma.projectDocument.update({
      where: { id: documentId },
      data: { status: "READY" },
    })

    return {
      chunks: chunkRecords,
      totalChunks: chunkRecords.length,
      processingTime: Date.now() - now.getTime(),
    }
  }
}

/**
 * Enhanced semantic search in documents with construction-specific context
 */
export async function semanticSearch(query: string, projectId: string, limit = 5) {
  const startTime = Date.now()
  const queryEmbedding = await generateEmbedding(query)

  logger.log("Starting semantic search", { query, projectId, limit })

  try {
    // Get project summary for context
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      select: { projectSummary: true, name: true },
    })

    let projectSimilarity = 0
    if (project?.projectSummary) {
      // Split project summary into chunks for better matching
      const chunks = project.projectSummary.split("\n\n").filter((chunk) => chunk.trim().length > 0)

      if (chunks.length > 0) {
        const embeddings = await generateEmbeddings(chunks)

        // Calculate similarity for each chunk
        for (const embedding of embeddings) {
          const similarity = 1 - cosineDistance(queryEmbedding, embedding)
          if (similarity > projectSimilarity) {
            projectSimilarity = similarity
            // Early exit for high similarity
            if (projectSimilarity > 0.8) break
          }
        }
      }
    }

    // Enhanced document search with metadata
    const documents = await prisma.$queryRaw`
      SELECT
        pd.id,
        pd."originalFileName",
        pd."fileSummary" as content,
        pd."mimeType",
        pd.status,
        1 - (pd."fileSummaryEmbedding" <=> ${queryEmbedding}::vector) as similarity,
        f.name as "folderName"
      FROM "ProjectDocument" pd
      LEFT JOIN "Folder" f ON pd."folderId" = f.id
      WHERE pd."projectId" = ${projectId}
        AND pd."fileSummaryEmbedding" IS NOT NULL
        AND pd.status = 'READY'
      ORDER BY similarity DESC
      LIMIT ${limit}
    `

    // Get total count of searchable documents
    const documentsCountResult = await prisma.$queryRaw<{ count: string }[]>`
      SELECT COUNT(*)::text AS count
      FROM "ProjectDocument"
      WHERE "projectId" = ${projectId}
        AND "fileSummaryEmbedding" IS NOT NULL
        AND status = 'READY'
    `
    const documentsCount = parseInt(documentsCountResult[0]?.count || "0", 10)

    const processingTime = Date.now() - startTime

    logger.log("Semantic search completed", {
      query,
      projectId,
      documentsFound: Array.isArray(documents) ? documents.length : 0,
      totalDocuments: documentsCount,
      projectSimilarity,
      processingTime,
    })

    return {
      documents,
      documentsCount,
      projectSimilarity,
      processingTime,
      projectName: project?.name,
    }
  } catch (error) {
    logger.error("Semantic search failed", { query, projectId, error })
    throw new Error(`Semantic search failed: ${error instanceof Error ? error.message : "Unknown error"}`)
  }
}

// Fonction utilitaire pour calculer la distance cosinus entre deux vecteurs

/**
 * Enhanced semantic search in document chunks with better context
 */
export async function semanticSearchInDocumentChunks(projectId: string, query: string, limit = 10) {
  const startTime = Date.now()
  const queryEmbedding = await generateEmbedding(query)

  logger.log("Starting chunk-level semantic search", { projectId, query, limit })

  try {
    // Enhanced chunk search with document context
    const chunkResults = await prisma.$queryRaw`
      SELECT
        dc.id,
        dc.content,
        dc."pageNumber",
        dc.metadata,
        dc."projectDocumentId" as "documentId",
        pd."originalFileName",
        pd."mimeType",
        f.name as "folderName",
        1 - (dc.embedding <=> ${queryEmbedding}::vector) as similarity
      FROM "DocumentChunk" dc
      JOIN "ProjectDocument" pd ON dc."projectDocumentId" = pd.id
      LEFT JOIN "Folder" f ON pd."folderId" = f.id
      WHERE pd."projectId" = ${projectId}
        AND pd.status = 'READY'
        AND dc.embedding IS NOT NULL
      ORDER BY similarity DESC
      LIMIT ${limit}
    `

    // Get chunk statistics
    const chunkStatsResult = await prisma.$queryRaw<
      {
        totalChunks: string
        totalDocuments: string
      }[]
    >`
      SELECT
        COUNT(dc.id)::text as "totalChunks",
        COUNT(DISTINCT pd.id)::text as "totalDocuments"
      FROM "DocumentChunk" dc
      JOIN "ProjectDocument" pd ON dc."projectDocumentId" = pd.id
      WHERE pd."projectId" = ${projectId}
        AND pd.status = 'READY'
        AND dc.embedding IS NOT NULL
    `

    const stats = chunkStatsResult[0] || { totalChunks: "0", totalDocuments: "0" }
    const processingTime = Date.now() - startTime

    logger.log("Chunk search completed", {
      query,
      projectId,
      chunksFound: Array.isArray(chunkResults) ? chunkResults.length : 0,
      totalChunks: parseInt(stats.totalChunks, 10),
      totalDocuments: parseInt(stats.totalDocuments, 10),
      processingTime,
    })

    return {
      chunks: chunkResults,
      totalChunks: parseInt(stats.totalChunks, 10),
      totalDocuments: parseInt(stats.totalDocuments, 10),
      processingTime,
    }
  } catch (error) {
    logger.error("Chunk search failed", { projectId, query, error })
    throw new Error(`Chunk search failed: ${error instanceof Error ? error.message : "Unknown error"}`)
  }
}

/**
 * Combined semantic search that searches both document summaries and chunks
 */
export async function combinedSemanticSearch(
  query: string,
  projectId: string,
  options: {
    documentLimit?: number
    chunkLimit?: number
    minSimilarity?: number
  } = {}
) {
  const { documentLimit = 5, chunkLimit = 10, minSimilarity = 0.1 } = options
  const startTime = Date.now()

  logger.log("Starting combined semantic search", { query, projectId, options })

  try {
    // Search in document summaries first (Level 2)
    const documentResults = await semanticSearch(query, projectId, documentLimit)

    // Search in chunks for more detailed results (Level 3)
    const chunkResults = await semanticSearchInDocumentChunks(projectId, query, chunkLimit)

    // Filter results by minimum similarity
    const filteredDocuments = Array.isArray(documentResults.documents)
      ? (documentResults.documents as DocumentSearchResult[]).filter((doc) => (doc.similarity || 0) >= minSimilarity)
      : []

    const filteredChunks = Array.isArray(chunkResults.chunks)
      ? (chunkResults.chunks as ChunkSearchResult[]).filter((chunk) => (chunk.similarity || 0) >= minSimilarity)
      : []

    const processingTime = Date.now() - startTime

    logger.log("Combined search completed", {
      query,
      projectId,
      documentsFound: filteredDocuments.length,
      chunksFound: filteredChunks.length,
      processingTime,
    })

    return {
      documents: filteredDocuments,
      chunks: filteredChunks,
      totalDocuments: documentResults.documentsCount || 0,
      totalChunks: chunkResults.totalChunks || 0,
      projectSimilarity: documentResults.projectSimilarity || 0,
      processingTime,
      projectName: documentResults.projectName,
    }
  } catch (error) {
    logger.error("Combined search failed", { query, projectId, error })
    throw new Error(`Combined search failed: ${error instanceof Error ? error.message : "Unknown error"}`)
  }
}
