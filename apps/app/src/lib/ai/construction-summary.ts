import { OpenAI } from "openai"

import { env } from "@/lib/env"
import { prisma } from "@/lib/prisma"
import { ConstructionDocumentType, ConstructionMetadata } from "@/types/project"
import { logger } from "@buildismart/lib"

const openai = new OpenAI({
  apiKey: env.OPENAI_API_KEY,
})

/**
 * Identify the type of construction document based on content and filename
 */
export function identifyConstructionDocumentType(content: string, fileName: string): ConstructionDocumentType {
  const lowerContent = content.toLowerCase()
  const lowerFileName = fileName.toLowerCase()

  // Check filename first for quick identification
  if (lowerFileName.includes("cctp") || lowerFileName.includes("cahier des clauses")) {
    return { type: "CCTP", description: "Cahier des Clauses Techniques Particulières", priority: 1 }
  }
  if (lowerFileName.includes("dpgf") || lowerFileName.includes("détail")) {
    return { type: "DPGF", description: "Détail du Prix Global et Forfaitaire", priority: 2 }
  }
  if (lowerFileName.includes("plan") || lowerFileName.includes("dessin")) {
    return { type: "PLAN", description: "Plans et dessins techniques", priority: 3 }
  }
  if (lowerFileName.includes("devis") || lowerFileName.includes("prix")) {
    return { type: "DEVIS", description: "Devis estimatif", priority: 4 }
  }

  // Check content for document type indicators
  if (lowerContent.includes("cahier des clauses techniques") || lowerContent.includes("cctp")) {
    return { type: "CCTP", description: "Cahier des Clauses Techniques Particulières", priority: 1 }
  }
  if (lowerContent.includes("détail du prix") || lowerContent.includes("dpgf") || lowerContent.includes("bordereau")) {
    return { type: "DPGF", description: "Détail du Prix Global et Forfaitaire", priority: 2 }
  }
  if (lowerContent.includes("règlement de consultation") || lowerContent.includes("rc")) {
    return { type: "REGLEMENT", description: "Règlement de consultation", priority: 5 }
  }
  if (lowerContent.includes("cahier des charges") || lowerContent.includes("cdc")) {
    return { type: "CAHIER_CHARGES", description: "Cahier des charges", priority: 6 }
  }

  return { type: "AUTRE", description: "Autre document", priority: 10 }
}

/**
 * Extract technical elements from construction document content
 */
export function extractTechnicalElements(content: string): string[] {
  const elements: string[] = []
  const lowerContent = content.toLowerCase()

  // Common construction technical elements
  const technicalPatterns = [
    /béton\s+armé/gi,
    /fondations?\s+(?:superficielles?|profondes?)/gi,
    /structure\s+(?:métallique|bois|béton)/gi,
    /isolation\s+(?:thermique|phonique)/gi,
    /étanchéité/gi,
    /charpente/gi,
    /couverture/gi,
    /cloisons?/gi,
    /revêtements?/gi,
    /menuiseries?/gi,
    /plomberie/gi,
    /électricité/gi,
    /chauffage/gi,
    /ventilation/gi,
    /climatisation/gi,
    /terrassements?/gi,
    /voirie/gi,
    /assainissement/gi,
  ]

  for (const pattern of technicalPatterns) {
    const matches = content.match(pattern)
    if (matches) {
      elements.push(...matches.map(match => match.trim()))
    }
  }

  return [...new Set(elements)] // Remove duplicates
}

/**
 * Extract quantities and costs from construction documents
 */
export function extractQuantitiesAndCosts(content: string): { quantities: Record<string, number>; costs: Record<string, number> } {
  const quantities: Record<string, number> = {}
  const costs: Record<string, number> = {}

  // Patterns for quantities (m², m³, ml, etc.)
  const quantityPatterns = [
    /(\d+(?:[,.]?\d+)?)\s*m[²2]/gi,
    /(\d+(?:[,.]?\d+)?)\s*m[³3]/gi,
    /(\d+(?:[,.]?\d+)?)\s*ml/gi,
    /(\d+(?:[,.]?\d+)?)\s*m/gi,
    /(\d+(?:[,.]?\d+)?)\s*kg/gi,
    /(\d+(?:[,.]?\d+)?)\s*t/gi,
    /(\d+(?:[,.]?\d+)?)\s*u/gi,
  ]

  // Patterns for costs (€, EUR)
  const costPatterns = [
    /(\d+(?:[,.]?\d+)?)\s*€/gi,
    /(\d+(?:[,.]?\d+)?)\s*EUR/gi,
    /(\d+(?:[,.]?\d+)?)\s*euros?/gi,
  ]

  // Extract quantities
  for (const pattern of quantityPatterns) {
    const matches = [...content.matchAll(pattern)]
    for (const match of matches) {
      const value = parseFloat(match[1].replace(',', '.'))
      const unit = match[0].replace(match[1], '').trim()
      quantities[unit] = (quantities[unit] || 0) + value
    }
  }

  // Extract costs
  for (const pattern of costPatterns) {
    const matches = [...content.matchAll(pattern)]
    for (const match of matches) {
      const value = parseFloat(match[1].replace(',', '.'))
      const unit = match[0].replace(match[1], '').trim()
      costs[unit] = (costs[unit] || 0) + value
    }
  }

  return { quantities, costs }
}

/**
 * Generate construction-specific file summary with enhanced context
 */
export async function generateConstructionFileSummary(
  content: string,
  fileName: string,
  documentType?: ConstructionDocumentType
): Promise<{ summary: string; metadata: ConstructionMetadata }> {
  try {
    const truncatedContent = content.substring(0, 15000)
    const docType = documentType || identifyConstructionDocumentType(content, fileName)
    const technicalElements = extractTechnicalElements(content)
    const { quantities, costs } = extractQuantitiesAndCosts(content)

    // Create context-aware prompt based on document type
    let systemPrompt = `Tu es un expert en analyse de documents de construction (BTP - Bâtiment et Travaux Publics).
    Analyse ce document de type "${docType.description}" et fournis un résumé concis et technique.`

    let userPrompt = `Fichier: ${fileName}\nType: ${docType.description}\n\nContenu:\n${truncatedContent}`

    switch (docType.type) {
      case "CCTP":
        systemPrompt += `
        Focus sur:
        - Les spécifications techniques détaillées
        - Les matériaux et leurs caractéristiques
        - Les méthodes d'exécution
        - Les normes et réglementations
        - Les contraintes particulières
        Format: 2-3 lignes, maximum 250 caractères.`
        break

      case "DPGF":
        systemPrompt += `
        Focus sur:
        - Les postes de travaux et leurs quantités
        - Les prix unitaires significatifs
        - Le montant total estimé
        - Les variations possibles
        Format: 2-3 lignes, maximum 250 caractères.`
        break

      case "PLAN":
        systemPrompt += `
        Focus sur:
        - Le type de plan (architecture, structure, réseaux)
        - Les dimensions principales
        - Les éléments techniques représentés
        - L'échelle et les détails
        Format: 2-3 lignes, maximum 250 caractères.`
        break

      case "DEVIS":
        systemPrompt += `
        Focus sur:
        - Le montant total du devis
        - Les principaux postes de coûts
        - Les conditions commerciales
        - La validité de l'offre
        Format: 2-3 lignes, maximum 250 caractères.`
        break

      default:
        systemPrompt += `
        Focus sur les éléments les plus pertinents pour un appel d'offres de construction.
        Format: 2-3 lignes, maximum 250 caractères.`
    }

    const chatResponse = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userPrompt },
      ],
      temperature: 0.3,
      max_tokens: 200,
    })

    const summary = chatResponse.choices[0]?.message?.content?.trim() || "Résumé indisponible"

    // Create enhanced metadata
    const metadata: ConstructionMetadata = {
      wordCount: content.split(/\s+/).length,
      pageCount: content.includes("\f") ? content.split("\f").length : 1,
      size: content.length,
      lastModified: new Date().toISOString(),
      documentType: docType,
      technicalElements,
      quantities,
      costs,
      constraints: extractConstraints(content),
      specifications: extractSpecifications(content),
    }

    logger.log("Construction summary generated", {
      fileName,
      documentType: docType.type,
      summaryLength: summary.length,
      technicalElementsCount: technicalElements.length,
      quantitiesCount: Object.keys(quantities).length,
      costsCount: Object.keys(costs).length,
    })

    return { summary, metadata }
  } catch (error) {
    logger.error("Construction summary generation failed", { fileName, error })
    throw new Error(`Échec de la génération du résumé: ${error instanceof Error ? error.message : "Erreur inconnue"}`)
  }
}

/**
 * Extract constraints from construction documents
 */
function extractConstraints(content: string): string[] {
  const constraints: string[] = []
  const lowerContent = content.toLowerCase()

  const constraintPatterns = [
    /délai\s+(?:d'exécution|de livraison|contractuel)/gi,
    /contrainte\s+(?:technique|environnementale|réglementaire)/gi,
    /obligation\s+(?:de résultat|de moyen)/gi,
    /respect\s+(?:des normes|de la réglementation)/gi,
    /zone\s+(?:protégée|classée)/gi,
    /accès\s+(?:difficile|restreint)/gi,
    /nuisances?\s+(?:sonores?|environnementales?)/gi,
  ]

  for (const pattern of constraintPatterns) {
    const matches = content.match(pattern)
    if (matches) {
      constraints.push(...matches.map(match => match.trim()))
    }
  }

  return [...new Set(constraints)]
}

/**
 * Extract specifications from construction documents
 */
function extractSpecifications(content: string): string[] {
  const specifications: string[] = []

  const specPatterns = [
    /norme\s+(?:NF|EN|ISO)\s*[\d-]+/gi,
    /DTU\s*[\d.]+/gi,
    /classe\s+(?:de résistance|d'exposition|de performance)/gi,
    /certification\s+(?:CE|NF|CSTB)/gi,
    /performance\s+(?:thermique|acoustique|mécanique)/gi,
  ]

  for (const pattern of specPatterns) {
    const matches = content.match(pattern)
    if (matches) {
      specifications.push(...matches.map(match => match.trim()))
    }
  }

  return [...new Set(specifications)]
}

/**
 * Generate project-level summary from multiple document summaries
 */
export async function generateProjectSummary(projectId: string): Promise<string> {
  try {
    // Get all document summaries for the project
    const documents = await prisma.projectDocument.findMany({
      where: { projectId, status: "READY" },
      select: { originalFileName: true, fileSummary: true, mimeType: true },
    })

    if (documents.length === 0) {
      return "Projet sans documents analysés"
    }

    // Combine all summaries
    const combinedContent = documents
      .map(doc => `${doc.originalFileName}: ${doc.fileSummary || "Pas de résumé"}`)
      .join("\n\n")

    const chatResponse = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        {
          role: "system",
          content: `Tu es un expert en analyse de projets de construction (BTP).
          Génère un résumé de projet global en 2-3 lignes maximum.
          Focus sur:
          - Le type de projet de construction
          - Les éléments techniques principaux
          - L'envergure du projet
          - Les spécificités importantes
          Style: Concis, technique, adapté aux appels d'offres.
          Maximum 300 caractères.`,
        },
        {
          role: "user",
          content: `Résume ce projet de construction basé sur ${documents.length} documents:\n\n${combinedContent.substring(0, 10000)}`,
        },
      ],
      temperature: 0.3,
      max_tokens: 200,
    })

    const summary = chatResponse.choices[0]?.message?.content?.trim() || "Résumé de projet indisponible"

    // Update project summary in database
    await prisma.project.update({
      where: { id: projectId },
      data: { projectSummary: summary },
    })

    logger.log("Project summary generated", {
      projectId,
      documentsCount: documents.length,
      summaryLength: summary.length,
    })

    return summary
  } catch (error) {
    logger.error("Project summary generation failed", { projectId, error })
    throw new Error(`Échec de la génération du résumé de projet: ${error instanceof Error ? error.message : "Erreur inconnue"}`)
  }
}
