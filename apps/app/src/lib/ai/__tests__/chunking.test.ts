import { describe, it, expect, beforeEach, vi } from 'vitest'
import { DocumentChunker } from '../chunking'

// Mock dependencies
vi.mock('@/lib/prisma', () => ({
  prisma: {
    projectDocument: {
      findUnique: vi.fn(),
      update: vi.fn()
    },
    documentChunk: {
      deleteMany: vi.fn(),
      create: vi.fn()
    },
    $transaction: vi.fn()
  }
}))

vi.mock('@/lib/ai/embeddings', () => ({
  generateEmbeddings: vi.fn().mockResolvedValue([
    [0.1, 0.2, 0.3], // Mock embedding vectors
    [0.4, 0.5, 0.6],
    [0.7, 0.8, 0.9]
  ])
}))

vi.mock('@buildismart/lib', () => ({
  logger: {
    log: vi.fn(),
    error: vi.fn()
  }
}))

describe('DocumentChunker', () => {
  let chunker: DocumentChunker

  beforeEach(() => {
    vi.clearAllMocks()
    chunker = new DocumentChunker()
  })

  describe('Text Chunking', () => {
    it('should chunk simple text into paragraphs', () => {
      const text = `Premier paragraphe avec du contenu.

Deuxième paragraphe avec plus de contenu.

Troisième paragraphe final.`

      const chunks = chunker.chunkText(text, 'test.txt')

      expect(chunks).toHaveLength(3)
      expect(chunks[0].content).toContain('Premier paragraphe')
      expect(chunks[1].content).toContain('Deuxième paragraphe')
      expect(chunks[2].content).toContain('Troisième paragraphe')
    })

    it('should identify construction document sections', () => {
      const text = `ARTICLE 1 - GÉNÉRALITÉS
Cet article traite des généralités du projet.

ARTICLE 2 - MATÉRIAUX
Description des matériaux à utiliser.

CHAPITRE 3 - EXÉCUTION
Méthodes d'exécution des travaux.`

      const chunks = chunker.chunkText(text, 'cctp.txt')

      expect(chunks.length).toBeGreaterThan(0)
      expect(chunks.some(chunk => chunk.metadata.section === 'article')).toBe(true)
      expect(chunks.some(chunk => chunk.metadata.section === 'chapitre')).toBe(true)
    })

    it('should handle large text by splitting appropriately', () => {
      const longText = 'A'.repeat(5000) // Text longer than default max chunk size
      
      const chunks = chunker.chunkText(longText, 'large.txt')

      expect(chunks.length).toBeGreaterThan(1)
      chunks.forEach(chunk => {
        expect(chunk.content.length).toBeLessThanOrEqual(1000) // Default max chunk size
      })
    })

    it('should preserve overlap between chunks', () => {
      const chunkerWithOverlap = new DocumentChunker({ 
        maxChunkSize: 100, 
        overlapSize: 20,
        preserveParagraphs: false 
      })
      
      const text = 'A'.repeat(200)
      const chunks = chunkerWithOverlap.chunkText(text, 'overlap.txt')

      expect(chunks.length).toBeGreaterThan(1)
      // Check that there's some overlap between consecutive chunks
      if (chunks.length > 1) {
        const firstChunkEnd = chunks[0].content.slice(-10)
        const secondChunkStart = chunks[1].content.slice(0, 10)
        // There should be some similarity due to overlap
        expect(firstChunkEnd).toBeTruthy()
        expect(secondChunkStart).toBeTruthy()
      }
    })

    it('should filter out chunks that are too small', () => {
      const chunkerWithMinSize = new DocumentChunker({ 
        minChunkSize: 50,
        maxChunkSize: 100 
      })
      
      const text = `Short.

This is a longer paragraph that should be included because it meets the minimum size requirement for chunks.

Tiny.`

      const chunks = chunkerWithMinSize.chunkText(text, 'mixed.txt')

      // Should filter out very short chunks
      chunks.forEach(chunk => {
        expect(chunk.content.length).toBeGreaterThanOrEqual(50)
      })
    })
  })

  describe('Construction Document Analysis', () => {
    it('should identify CCTP document type', () => {
      const text = 'CAHIER DES CLAUSES TECHNIQUES PARTICULIÈRES\nSpécifications pour béton armé'
      
      const chunks = chunker.chunkText(text, 'cctp_project.pdf')

      expect(chunks.length).toBeGreaterThan(0)
      expect(chunks[0].metadata.section).toBeDefined()
    })

    it('should identify DPGF document type', () => {
      const text = 'DÉTAIL DU PRIX GLOBAL ET FORFAITAIRE\nPoste 1: Terrassement - 1500€'
      
      const chunks = chunker.chunkText(text, 'dpgf_project.pdf')

      expect(chunks.length).toBeGreaterThan(0)
      expect(chunks[0].metadata.section).toBeDefined()
    })

    it('should handle technical construction terms', () => {
      const text = `ARTICLE 1 - BÉTON ARMÉ
Le béton utilisé sera de classe C25/30.
L'isolation thermique sera conforme à la RT2012.
Les fondations profondes seront réalisées par pieux.`

      const chunks = chunker.chunkText(text, 'specifications.txt')

      expect(chunks.length).toBeGreaterThan(0)
      expect(chunks[0].content).toContain('BÉTON ARMÉ')
    })
  })

  describe('Metadata Generation', () => {
    it('should generate proper metadata for chunks', () => {
      const text = `SECTION 1
Premier contenu de section.

SECTION 2  
Deuxième contenu de section.`

      const chunks = chunker.chunkText(text, 'sections.txt')

      chunks.forEach((chunk, index) => {
        expect(chunk.metadata).toMatchObject({
          chunkIndex: expect.any(Number),
          section: expect.any(String),
          startPosition: expect.any(Number),
          endPosition: expect.any(Number)
        })
      })
    })

    it('should handle empty or whitespace-only text', () => {
      const emptyText = '   \n\n   \t   \n   '
      
      const chunks = chunker.chunkText(emptyText, 'empty.txt')

      expect(chunks).toHaveLength(0)
    })

    it('should clean and normalize text properly', () => {
      const messyText = 'Text\t\twith\r\n\r\nmultiple\n\n\nspaces   and   tabs\t\t\t'
      
      const chunks = chunker.chunkText(messyText, 'messy.txt')

      expect(chunks.length).toBeGreaterThan(0)
      expect(chunks[0].content).not.toContain('\t')
      expect(chunks[0].content).not.toContain('\r')
      expect(chunks[0].content).not.toMatch(/\s{3,}/) // No triple spaces
    })
  })
})
