import { describe, it, expect, beforeEach, vi } from 'vitest'
import { extractContentFromFile } from '../content-extraction'

// Mock dependencies
vi.mock('openai', () => ({
  OpenAI: vi.fn().mockImplementation(() => ({
    chat: {
      completions: {
        create: vi.fn().mockResolvedValue({
          choices: [{ message: { content: 'Test summary for construction document' } }]
        })
      }
    }
  }))
}))

vi.mock('pdf-ts', () => ({
  pdfToText: vi.fn().mockResolvedValue('Test PDF content extracted')
}))

vi.mock('mammoth', () => ({
  extractRawText: vi.fn().mockResolvedValue({ value: 'Test Word content extracted' })
}))

vi.mock('tesseract.js', () => ({
  createWorker: vi.fn().mockResolvedValue({
    load: vi.fn(),
    loadLanguage: vi.fn(),
    initialize: vi.fn(),
    recognize: vi.fn().mockResolvedValue({ data: { text: 'Test OCR content extracted' } }),
    terminate: vi.fn()
  })
}))

vi.mock('node-tika', () => ({
  extract: vi.fn().mockResolvedValue('Test tika content extracted')
}))

vi.mock('@/lib/env', () => ({
  env: {
    OPENAI_API_KEY: 'test-api-key'
  }
}))

vi.mock('@buildismart/lib', () => ({
  logger: {
    log: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
  }
}))

describe('Content Extraction', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should extract content from PDF file', async () => {
    const pdfData = new Uint8Array([37, 80, 68, 70]) // PDF header
    const file = new File([pdfData], 'test.pdf', { type: 'application/pdf' })

    const result = await extractContentFromFile(file)

    expect(result).toMatchObject({
      extractedText: expect.stringContaining('test.pdf'),
      summary: expect.any(String),
      metadata: {
        wordCount: expect.any(Number),
        pageCount: expect.any(Number),
        size: expect.any(Number),
        lastModified: expect.any(String)
      }
    })
  })

  it('should extract content from Word document', async () => {
    const wordData = new Uint8Array([80, 75, 3, 4]) // ZIP header (DOCX)
    const file = new File([wordData], 'test.docx', { 
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
    })

    const result = await extractContentFromFile(file)

    expect(result).toMatchObject({
      extractedText: expect.stringContaining('test.docx'),
      summary: expect.any(String),
      metadata: expect.objectContaining({
        wordCount: expect.any(Number),
        size: expect.any(Number)
      })
    })
  })

  it('should extract content from image using OCR', async () => {
    const imageData = new Uint8Array([137, 80, 78, 71]) // PNG header
    const file = new File([imageData], 'test.png', { type: 'image/png' })

    const result = await extractContentFromFile(file)

    expect(result).toMatchObject({
      extractedText: expect.stringContaining('test.png'),
      summary: expect.any(String),
      metadata: expect.objectContaining({
        wordCount: expect.any(Number),
        size: expect.any(Number)
      })
    })
  })

  it('should handle extraction errors gracefully', async () => {
    const file = new File([''], 'empty.txt', { type: 'text/plain' })
    
    // Mock OpenAI to throw an error
    const mockOpenAI = await import('openai')
    vi.mocked(mockOpenAI.OpenAI).mockImplementationOnce(() => {
      throw new Error('OpenAI API error')
    })

    await expect(extractContentFromFile(file)).rejects.toThrow('Échec de l\'extraction de contenu')
  })

  it('should generate construction-specific metadata', async () => {
    const textData = 'CCTP - Cahier des Clauses Techniques Particulières\nBéton armé C25/30\nIsolation thermique'
    const file = new File([textData], 'cctp.txt', { type: 'text/plain' })

    const result = await extractContentFromFile(file)

    expect(result.extractedText).toContain('CCTP')
    expect(result.summary).toBeTruthy()
    expect(result.metadata.wordCount).toBeGreaterThan(0)
  })
})

describe('Content Extraction Integration', () => {
  it('should handle various file types consistently', async () => {
    const testFiles = [
      { name: 'test.pdf', type: 'application/pdf', data: new Uint8Array([37, 80, 68, 70]) },
      { name: 'test.docx', type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', data: new Uint8Array([80, 75, 3, 4]) },
      { name: 'test.txt', type: 'text/plain', data: new TextEncoder().encode('Test content') }
    ]

    for (const testFile of testFiles) {
      const file = new File([testFile.data], testFile.name, { type: testFile.type })
      const result = await extractContentFromFile(file)

      expect(result).toMatchObject({
        extractedText: expect.stringContaining(testFile.name),
        summary: expect.any(String),
        metadata: expect.objectContaining({
          wordCount: expect.any(Number),
          size: expect.any(Number),
          lastModified: expect.any(String)
        })
      })
    }
  })
})
