import { trpcClient } from "@/lib/trpc/client" // bien utiliser .client ici
import { FolderType } from "@/types/index-type"
type PayloadType = {
  files: {
    name: string
    type: string
    size: number
    data: number[]
  }[]
  parentId: string
}
export async function getAllFolders() {
  return await trpcClient.folders.getAll.query()
}

export async function getFolderById(id: string) {
  return await trpcClient.folders.getById.query({ id })
}

export async function getFileById(id: string) {
  return await trpcClient.folders.getFileById.query({ id })
}

export async function upsertItem(item: FolderType) {
  return await trpcClient.folders.upsert.mutate({ item })
}

export async function deleteItem(id: string) {
  return await trpcClient.folders.delete.mutate({ id })
}

export async function uploadFile(file: File, parentId: string | null) {
  const fileId = `file-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

  // Lire le contenu du fichier avant l'envoi
  const arrayBuffer = await file.arrayBuffer()
  const uint8Array = new Uint8Array(arrayBuffer)

  const response = await trpcClient.upload.uploadFile.mutate({
    file: {
      name: file.name,
      type: file.type,
      size: file.size,
      data: Array.from(uint8Array), // Convertir en array simple pour la sérialisation
    },
    fileId,
    parentId,
  })

  return {
    id: fileId,
    title: file.name,
    type: "file",
    createdAt: new Date(),
    parentId,
    url: response.key,
    mimeType: file.type,
  }
}

export async function uploadFolder(files: File[], parentId: string | null) {
  try {
    // Préparer les fichiers pour l'envoi
    const preparedFiles = await Promise.all(
      files.map(async (file) => {
        const arrayBuffer = await file.arrayBuffer()
        const uint8Array = new Uint8Array(arrayBuffer)
        return {
          name: file.name,
          type: file.type,
          size: file.size,
          data: Array.from(uint8Array),
        }
      })
    )

    const payload: PayloadType = {
      files: [],
      parentId: "",
    }
    if (preparedFiles.length > 0) {
      payload.files = preparedFiles
    }
    if (parentId) {
      payload.parentId = parentId
    }

    const response = await trpcClient.upload.uploadFolder.mutate(payload)

    return response.items
  } catch (error) {
    console.error("Upload folder error:", error)
    throw error
  }
}
export async function generateEmbedding(text: string) {
  // Si vous voulez vraiment générer côté client (attention aux limites)
  // Sinon, passez par tRPC
  const response = await trpcClient.folders.generateEmbeddings.mutate({
    text,
  })
  return response.embedding
}

export async function updateDocumentEmbedding(documentId: string) {
  return await trpcClient.folders.generateEmbeddings.mutate({
    documentId,
  })
}

export async function updateProjectEmbedding(projectId: string) {
  return await trpcClient.folders.generateEmbeddings.mutate({
    projectId,
  })
}

export async function chunkAndEmbedDocument(documentId: string) {
  return await trpcClient.folders.generateEmbeddings.mutate({
    documentId,
    chunk: true,
  })
}
