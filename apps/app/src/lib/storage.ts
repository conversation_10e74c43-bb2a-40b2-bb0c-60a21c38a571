// lib/storage.ts
interface StoredFile {
  id: string
  name: string
  type: string
  content: <PERSON><PERSON>yBuffer
}

interface StoredFolder {
  id: string
  name: string
  files: StoredFile[]
}

const storage = {
  files: new Map<string, StoredFile>(),
  folders: new Map<string, StoredFolder>(),
}

export async function storeFile(file: File): Promise<string> {
  const id = `${Date.now()}-${file.name}`
  const content = await file.arrayBuffer()
  storage.files.set(id, {
    id,
    name: file.name,
    type: file.type,
    content,
  })
  return id
}

export async function storeFolder(files: File[]): Promise<string> {
  const folderName = files[0]?.webkitRelativePath?.split("/")[0] || `Dossier-${Date.now()}`
  const id = `${Date.now()}-${folderName}`

  const storedFiles: StoredFile[] = []
  for (const file of files) {
    const fileId = await storeFile(file)
    const storedFile = storage.files.get(fileId)
    if (storedFile) storedFiles.push(storedFile)
  }

  storage.folders.set(id, {
    id,
    name: folderName,
    files: storedFiles,
  })

  return id
}

export async function getFileById(id: string): Promise<File | null> {
  const storedFile = storage.files.get(id)
  if (!storedFile) return null

  return new File([storedFile.content], storedFile.name, { type: storedFile.type })
}

export async function getFolderById(id: string): Promise<{ name: string; files: File[] } | null> {
  const storedFolder = storage.folders.get(id)
  if (!storedFolder) return null

  const files: File[] = []
  for (const storedFile of storedFolder.files) {
    files.push(new File([storedFile.content], storedFile.name, { type: storedFile.type }))
  }

  return {
    name: storedFolder.name,
    files,
  }
}
