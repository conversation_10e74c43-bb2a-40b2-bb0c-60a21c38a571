import { Prisma } from "@prisma/client"

// Base types for the multi-level RAG system
export interface ProjectSummary {
  id: string
  name: string
  description?: string
  projectSummary?: string
  projectSummaryEmbedding?: number[]
  createdAt: Date
  updatedAt: Date
}

export interface DocumentSummary {
  id: string
  originalFileName: string
  mimeType: string
  status: ProjectDocumentStatus
  extractedText?: string
  fileSummary?: string
  fileSummaryEmbedding?: number[]
  metadata?: DocumentMetadata
  createdAt: Date
  updatedAt: Date
}

export interface DocumentChunk {
  id: string
  content: string
  embedding?: number[]
  pageNumber?: number
  metadata?: ChunkMetadata
  projectDocumentId: string
  createdAt: Date
  updatedAt: Date
}

// Status enums
export enum ProjectDocumentStatus {
  UPLOADED = "UPLOADED",
  PROCESSING_TEXT = "PROCESSING_TEXT",
  PROCESSING_OCR = "PROCESSING_OCR",
  SUMMARIZING_FILE = "SUMMARIZING_FILE",
  INDEXING_CHUNKS = "INDEXING_CHUNKS",
  READY = "READY",
  ERROR = "ERROR",
}

// Metadata interfaces
export interface DocumentMetadata {
  wordCount: number
  pageCount: number
  size: number
  lastModified: string
  extractionMethod?: "pdf" | "ocr" | "word" | "tika" | "text"
  ocrPerformed?: boolean
  processingDuration?: number
}

export interface ChunkMetadata {
  documentId: string
  chunkIndex: number
  startPosition?: number
  endPosition?: number
  pageNumber?: number
  section?: string
  createdAt: string
}

// Processing pipeline types
export interface ProcessingResult {
  success: boolean
  documentId: string
  status: "READY" | "ERROR" | "PROCESSING_TEXT" | "PROCESSING_OCR" | "SUMMARIZING_FILE" | "INDEXING_CHUNKS"
  error?: string
  extractedText?: string
  summary?: string
  chunksCreated?: number
  processingTime?: number
}

export interface UploadResult {
  success: boolean
  fileId: string
  documentId?: string
  s3Key: string
  error?: string
}

// Search and retrieval types
export interface SearchResult {
  documents: DocumentSearchResult[]
  chunks: ChunkSearchResult[]
  totalDocuments: number
  totalChunks: number
  query: string
  processingTime: number
}

export interface DocumentSearchResult {
  id: string
  originalFileName: string
  content: string
  similarity: number
  metadata?: DocumentMetadata
}

export interface ChunkSearchResult {
  id: string
  content: string
  similarity: number
  documentId: string
  originalFileName?: string
  pageNumber?: number
  metadata?: ChunkMetadata
}

// Folder hierarchy types
export interface FolderHierarchy {
  id: string
  name: string
  type: "folder" | "file" | "project"
  parentId?: string
  projectId?: string
  children?: FolderHierarchy[]
  documents?: DocumentSummary[]
  metadata?: {
    fileCount: number
    totalSize: number
    lastModified: Date
  }
}

// Chat and AI types
export interface ChatContext {
  projectId: string
  projectSummary?: string
  relevantDocuments: DocumentSearchResult[]
  relevantChunks: ChunkSearchResult[]
  query: string
}

export interface ToolExecutionStatus {
  toolName: string
  status: "executing" | "completed" | "error"
  message: string
  startTime: Date
  endTime?: Date
  result?: unknown
}

// Prisma relation types with proper typing
export type ProjectWithDocuments = Prisma.ProjectGetPayload<{
  include: {
    allProjectDocuments: {
      include: {
        file: true
        folder: true
        documentChunks: true
      }
    }
    rootFolders: {
      include: {
        children: true
        documents: true
      }
    }
  }
}>

export type DocumentWithRelations = Prisma.ProjectDocumentGetPayload<{
  include: {
    file: true
    project: true
    folder: true
    documentChunks: true
  }
}>

export type FolderWithRelations = Prisma.FolderGetPayload<{
  include: {
    project: true
    parent: true
    children: true
    documents: {
      include: {
        file: true
      }
    }
    documentChunks: true
  }
}>

// API response types
export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  error?: string
  message?: string
  timestamp: string
}

export interface PaginatedResponse<T = unknown> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Construction-specific types
export interface ConstructionDocumentType {
  type: "CCTP" | "DPGF" | "PLAN" | "DEVIS" | "CAHIER_CHARGES" | "REGLEMENT" | "AUTRE"
  description: string
  priority: number
}

export interface ConstructionMetadata extends DocumentMetadata {
  documentType?: ConstructionDocumentType
  technicalElements?: string[]
  quantities?: Record<string, number>
  costs?: Record<string, number>
  constraints?: string[]
  specifications?: string[]
}
