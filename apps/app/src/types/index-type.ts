import { Prisma } from "@prisma/client"

export type FolderType = {
  fileSummaryEmbedding?: number[]
  folderEmbedding?: number[]
  chunkEmbeddings?: number[][]
  projectSummaryEmbedding?: number[]
  projectSummary?: string
  texteextraitdossier?: string
  id: string
  title: string
  mimeType?: string
  Ismodified?: boolean
  textextraitfichier?: string
  nombrefichiers?: number
  parentId: string | null
  description?: string
  summary?: string
  createdAt: Date
  updatedAt?: Date
  type: "folder" | "file"
  url?: string
  files?: Array<{
    fileSummaryEmbedding?: number[]

    id?: string
    name: string
    type: string
    url?: string
    summary?: string // ✅ Ajouté
    textextraitfichier?: string // ✅ Ajouté
  }>
  children?: FolderType[]
  size?: number
  lastModified?: string
  owner?: string
}
export type ProjectType = Prisma.ProjectGetPayload<{
  include: {
    rootFolders: {
      include: {
        children: {
          include: {
            children: {
              include: {
                children: true
                documents: {
                  include: {
                    file: true
                    folder: true
                    project: true
                  }
                }
              }
            }
            documents: {
              include: {
                file: true
                folder: true
                project: true
              }
            }
          }
        }
        documents: {
          include: {
            file: true
            folder: true
            project: true
          }
        }
      }
    }
    allProjectDocuments: {
      include: {
        file: true
      }
    }
  }
}> & {
  projectSummaryEmbedding?: number[] | undefined // ✅ ou Prisma.JsonValue si c'est du JSON dans Prisma
}
