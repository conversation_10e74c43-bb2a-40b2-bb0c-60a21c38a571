import { contentExtractionRouter } from "@/api/routers/content-extraction"
import { foldersRouter } from "@/api/routers/folders"
import { uploadRouter } from "@/api/routers/upload"

import { router } from "../lib/server/trpc"

import { authRouter } from "./auth/_router"
import { meRouter } from "./me/_router"

export const appRouter = router({
  auth: authRouter,
  me: meRouter,
  folders: foldersRouter,
  upload: uploadRouter,
  contentExtraction: contentExtractionRouter,
})

export type AppRouter = typeof appRouter
