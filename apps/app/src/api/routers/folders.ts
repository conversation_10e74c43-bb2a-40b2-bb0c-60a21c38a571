import { z } from "zod"

import {
  chunkAndEmbedDocument,
  generateEmbedding,
  updateDocumentEmbedding,
  updateProjectEmbedding,
} from "@/lib/ai/embeddings"
import { auth } from "@/lib/auth"
import { env } from "@/lib/env"
import { prisma } from "@/lib/prisma"
import { s3Client } from "@/lib/s3"
import { publicProcedure, router } from "@/lib/server/trpc"
import type { FolderType, ProjectType } from "@/types/index-type"
import { DeleteObjectCommand } from "@aws-sdk/client-s3"
import { ProjectDocument } from "@prisma/client"
import { TRPCError } from "@trpc/server"
type ProjectFolder = ProjectType["rootFolders"][number]

export const foldersRouter = router({
  // Récupérer tous les dossiers et fichiers
  getAll: publicProcedure.query(async ({}) => {
    const session = await auth()
    if (!session?.user?.id) {
      throw new TRPCError({ code: "UNAUTHORIZED" })
    }

    try {
      const projects = await prisma.project.findMany({
        where: { userId: session.user.id },
        include: {
          rootFolders: {
            include: {
              children: {
                include: {
                  children: true,
                  documents: {
                    include: {
                      file: true,
                      // Ajoutez ceci pour avoir les mêmes données que getById
                      folder: true,
                      project: true,
                    },
                  },
                },
              },
              documents: {
                include: {
                  file: true,
                  // Ajoutez ceci aussi
                  folder: true,
                  project: true,
                },
              },
            },
          },
          // Ajoutez ceci pour inclure les documents directement liés au projet
          allProjectDocuments: {
            include: {
              file: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      })

      return convertToFolderType(projects)
    } catch (error) {
      console.error("Database error:", error)
      throw new TRPCError({ code: "INTERNAL_SERVER_ERROR", message: "Failed to load folder structure" })
    }
  }),
  generateEmbeddings: publicProcedure
    .input(
      z.object({
        text: z.string().optional(),
        documentId: z.string().optional(),
        projectId: z.string().optional(),
        chunk: z.boolean().optional().default(false),
      })
    )
    .mutation(async ({ input }) => {
      try {
        if (input.text) {
          // Générer un embedding pour un texte simple
          const embedding = await generateEmbedding(input.text)
          return { embedding }
        }

        if (input.documentId) {
          if (input.chunk) {
            await chunkAndEmbedDocument(input.documentId)
          }
          await updateDocumentEmbedding(input.documentId)
        }

        if (input.projectId) {
          await updateProjectEmbedding(input.projectId)
        }

        return { success: true }
      } catch (error) {
        console.error("Embedding generation error:", error)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate embeddings",
        })
      }
    }),

  updateProjectSummary: publicProcedure
    .input(
      z.object({
        projectId: z.string(),
        summary: z.string(),
      })
    )
    .mutation(async ({ input }) => {
      const session = await auth()
      if (!session?.user?.id) {
        throw new TRPCError({ code: "UNAUTHORIZED" })
      }

      try {
        // Mettre à jour le projectSummary dans la base de données
        const updatedProject = await prisma.project.update({
          where: {
            id: input.projectId,
            userId: session.user.id, // Vérification de propriété
          },
          data: {
            projectSummary: input.summary,
          },
        })

        return { success: true, project: updatedProject }
      } catch (error) {
        console.error("Error updating project summary:", error)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update project summary",
        })
      }
    }),
  // Récupérer un dossier par ID
  getById: publicProcedure.input(z.object({ id: z.string() })).query(async ({ input }) => {
    try {
      // D'abord vérifier si c'est un projet
      const project = await prisma.project.findUnique({
        where: { id: input.id },
        include: {
          rootFolders: {
            include: {
              children: {
                include: {
                  children: true,
                  documents: {
                    include: {
                      file: true,
                      folder: true,
                      project: true,
                    },
                  },
                },
              },
              documents: {
                include: {
                  file: true,
                  folder: true,
                  project: true,
                },
              },
            },
          },
          allProjectDocuments: true,
        },
      })

      if (project) {
        // Récupérer tous les documents du projet (racine + dossiers)
        const allDocuments = [
          ...(project.allProjectDocuments || []),
          ...project.rootFolders.flatMap((folder) =>
            folder.documents.concat(folder.children.flatMap((child) => child.documents))
          ),
        ]

        // Générer le projectSummary en concaténant tous les fileSummary des documents
        const projectSummary = allDocuments
          .map((d) => d.fileSummary)
          .filter(Boolean)
          .join("\n\n")

        // Mettre à jour le projectSummary dans la base de données
        await prisma.project.update({
          where: { id: input.id },
          data: {
            projectSummary: projectSummary || null,
          },
        })

        return {
          id: project.id,
          title: project.name,
          type: "folder" as const,
          createdAt: project.createdAt,
          parentId: null,
          description: project.description || undefined,
          nombrefichiers: allDocuments.length / 2,
          texteextraitdossier: allDocuments
            .map((d) => d.extractedText)
            .filter(Boolean)
            .join("\n\n"),
          projectSummary: projectSummary || undefined,

          // ✅ Ajout des fichiers
          files: allDocuments.map((doc) => ({
            id: doc.id,
            name: doc.originalFileName || "Sans nom",
            type: /*doc.type ||*/ "file",
            summary: doc.fileSummary || "",
            textextraitfichier: doc.extractedText || "",
          })),
        }
      }

      // Le reste du code pour les dossiers normaux reste inchangé...
      const folder = await prisma.folder.findUnique({
        where: { id: input.id },
        include: {
          documents: {
            include: {
              file: true,
            },
          },
          project: true,
          children: true,
        },
      })

      if (!folder) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Item not found" })
      }

      return {
        id: folder.id,
        title: folder.name,
        type: "folder" as const,
        createdAt: folder.createdAt,
        parentId: folder.parentId,
        nombrefichiers: folder.documents?.length || 0,
        texteextraitdossier: folder.documents
          .map((doc) => doc.extractedText)
          .filter(Boolean)
          .join("\n\n"),

        files: folder.documents.map((doc) => ({
          id: doc.file?.id || doc.id,
          name: doc.file?.name || doc.originalFileName || "Sans nom",
          summary: doc.fileSummary || "",
          type: doc.file?.type || "file",
          textextraitfichier: doc.extractedText || "",
        })),
      }
    } catch (error) {
      console.error("Error in getById:", error)
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch item",
      })
    }
  }),

  // Récupérer le statut de traitement d'un projet
  getProjectStatus: publicProcedure.input(z.object({ projectId: z.string() })).query(async ({ input }) => {
    const session = await auth()
    if (!session?.user?.id) {
      throw new TRPCError({ code: "UNAUTHORIZED" })
    }

    try {
      const project = await prisma.project.findUnique({
        where: { id: input.projectId },
        include: {
          allProjectDocuments: {
            select: {
              id: true,
              originalFileName: true,
              status: true,
              processingError: true,
              createdAt: true,
              updatedAt: true,
            },
          },
        },
      })

      if (!project) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Project not found" })
      }

      // Vérifier que l'utilisateur a accès à ce projet
      if (project.userId !== session.user.id) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Access denied" })
      }

      const documents = project.allProjectDocuments
      const totalDocuments = documents.length

      if (totalDocuments === 0) {
        return {
          status: "NO_DOCUMENTS",
          message: "Aucun document téléchargé",
          progress: 0,
          totalDocuments: 0,
          readyDocuments: 0,
          processingDocuments: 0,
          errorDocuments: 0,
          isComplete: false,
        }
      }

      const readyDocuments = documents.filter((doc) => doc.status === "READY").length
      const errorDocuments = documents.filter((doc) => doc.status === "ERROR").length
      const processingDocuments = documents.filter((doc) =>
        ["UPLOADED", "PROCESSING_TEXT", "PROCESSING_OCR", "SUMMARIZING_FILE", "INDEXING_CHUNKS"].includes(doc.status)
      ).length

      const progress = Math.round((readyDocuments / totalDocuments) * 100)
      const isComplete = readyDocuments === totalDocuments && errorDocuments === 0

      let status: string
      let message: string

      if (isComplete) {
        status = "COMPLETE"
        message = "Analyse terminée"
      } else if (errorDocuments > 0) {
        status = "ERROR"
        message = `Erreur lors du traitement de ${errorDocuments} document(s)`
      } else if (processingDocuments > 0) {
        status = "PROCESSING"
        message = `Traitement en cours... (${readyDocuments}/${totalDocuments} terminés)`
      } else {
        status = "PENDING"
        message = "En attente de traitement"
      }

      return {
        status,
        message,
        progress,
        totalDocuments,
        readyDocuments,
        processingDocuments,
        errorDocuments,
        isComplete,
        documents: documents.map((doc) => ({
          id: doc.id,
          fileName: doc.originalFileName,
          status: doc.status,
          error: doc.processingError,
          createdAt: doc.createdAt,
          updatedAt: doc.updatedAt,
        })),
      }
    } catch (error) {
      console.error("Error fetching project status:", error)
      throw new TRPCError({ code: "INTERNAL_SERVER_ERROR", message: "Failed to fetch project status" })
    }
  }),

  getFileById: publicProcedure.input(z.object({ id: z.string() })).query(async ({ input }) => {
    try {
      const file = await prisma.projectDocument.findUnique({
        where: { id: input.id },
        include: {
          file: true,
          folder: true,
          project: true,
        },
      })

      if (!file) {
        throw new TRPCError({ code: "NOT_FOUND", message: "File not found" })
      }

      return {
        id: file.id,
        title: file.originalFileName,
        type: "file" as const,
        createdAt: file.createdAt,
        parentId: file.folderId,
        textextraitfichier: file.extractedText,
        summary: file.fileSummary,
        url: file.file?.key || "",
      }
    } catch (error) {
      console.error("Error fetching file:", error)
      throw new TRPCError({ code: "INTERNAL_SERVER_ERROR", message: "Failed to fetch file" })
    }
  }),

  // Créer ou mettre à jour un élément
  upsert: publicProcedure
    .input(
      z.object({
        item: z.object({
          id: z.string(),
          title: z.string().optional(),
          type: z.enum(["folder", "file"]),
          parentId: z.string().nullable().optional(),
          description: z.string().optional(),
          textextraitfichier: z.string().optional(),
          summary: z.string().optional(),
          texteextraitdossier: z.string().optional(),
        }),
      })
    )
    .mutation(async ({ input }) => {
      const session = await auth()
      if (!session?.user?.id) {
        throw new TRPCError({ code: "UNAUTHORIZED" })
      }

      try {
        const { item } = input

        if (item.type === "folder") {
          if (!item.title) {
            throw new TRPCError({ code: "BAD_REQUEST", message: "Folder title is required" })
          }

          // Vérifier si c'est un projet
          const isProject = await prisma.project.findUnique({
            where: { id: item.id },
          })

          if (isProject) {
            // Mettre à jour le projet
            await prisma.project.update({
              where: { id: item.id },
              data: {
                name: item.title,
                updatedAt: new Date(),
              },
            })
          } else {
            // Mettre à jour un dossier normal
            await prisma.folder.upsert({
              where: { id: item.id },
              create: {
                id: item.id,
                name: item.title,
                projectId: item.parentId || undefined,
                parentId: item.parentId || undefined,
              },
              update: {
                name: item.title,
                updatedAt: new Date(),
              },
            })
          }
        } else if (item.type === "file") {
          if (!item.title) {
            throw new TRPCError({ code: "BAD_REQUEST", message: "File name is required" })
          }

          const document = await prisma.projectDocument.findUnique({
            where: { id: item.id },
            include: { file: true },
          })

          if (!document) {
            throw new TRPCError({ code: "NOT_FOUND", message: "File not found" })
          }

          await prisma.projectDocument.update({
            where: { id: item.id },
            data: {
              originalFileName: item.title,
              extractedText: item.textextraitfichier || "",
              fileSummary: item.summary || "",
              updatedAt: new Date(),
            },
          })

          // Mettre à jour également le nom dans la table File si nécessaire
          if (document.file) {
            await prisma.file.update({
              where: { id: document.file.id },
              data: {
                name: item.title,
              },
            })
          }
        }

        return { success: true }
      } catch (error) {
        console.error("Update error:", error)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update folder structure",
          cause: error instanceof Error ? error.message : String(error),
        })
      }
    }),
  // Supprimer un élément
  delete: publicProcedure.input(z.object({ id: z.string() })).mutation(async ({ input }) => {
    const session = await auth()
    if (!session?.user?.id) {
      throw new TRPCError({ code: "UNAUTHORIZED" })
    }

    const { id } = input

    try {
      // Vérifier d'abord si c'est un projet
      const project = await prisma.project.findUnique({
        where: { id },
        include: {
          allProjectDocuments: {
            include: { file: true },
          },
          rootFolders: {
            include: {
              documents: {
                include: { file: true },
              },
              children: {
                include: {
                  documents: {
                    include: { file: true },
                  },
                  children: true,
                },
              },
            },
          },
        },
      })

      if (project) {
        // Récupérer tous les documents associés au projet
        const allDocuments = [
          ...(project.allProjectDocuments || []),
          ...project.rootFolders.flatMap((f) => [...f.documents, ...f.children.flatMap((c) => c.documents)]),
        ]

        // Supprimer physiquement les fichiers de S3
        await Promise.all(
          allDocuments.map(async (doc) => {
            if (doc.file) {
              const command = new DeleteObjectCommand({
                Bucket: env.NEXT_PUBLIC_S3_BUCKET_NAME,
                Key: doc.file.key,
              })
              await s3Client?.send(command)
            }
          })
        )

        // Supprimer en respectant l'ordre des dépendances
        await prisma.$transaction([
          // 1. Supprimer d'abord les documents (qui référencent les fichiers)
          prisma.projectDocument.deleteMany({
            where: {
              OR: [{ projectId: id }, { folder: { projectId: id } }],
            },
          }),
          // 2. Supprimer ensuite les dossiers
          prisma.folder.deleteMany({
            where: {
              OR: [{ projectId: id }, { parentId: id }],
            },
          }),
          // 3. Supprimer les fichiers (maintenant qu'aucun document ne les référence)
          prisma.file.deleteMany({
            where: {
              id: {
                in: allDocuments.map((doc) => doc.file?.id).filter(Boolean) as string[],
              },
            },
          }),
          // 4. Enfin, supprimer le projet lui-même
          prisma.project.delete({
            where: { id },
          }),
        ])

        return { success: true }
      }

      // Si ce n'est pas un projet, vérifier si c'est un dossier
      const folder = await prisma.folder.findUnique({
        where: { id },
        include: {
          documents: {
            include: { file: true },
          },
          children: {
            include: {
              documents: {
                include: { file: true },
              },
              children: true,
            },
          },
        },
      })

      if (folder) {
        // Récupérer tous les documents associés au dossier et ses sous-dossiers
        const allDocuments = [...folder.documents, ...folder.children.flatMap((c) => c.documents)]

        // Supprimer physiquement les fichiers de S3
        await Promise.all(
          allDocuments.map(async (doc) => {
            if (doc.file) {
              const command = new DeleteObjectCommand({
                Bucket: env.NEXT_PUBLIC_S3_BUCKET_NAME,
                Key: doc.file.key,
              })
              await s3Client?.send(command)
            }
          })
        )

        // Supprimer en respectant l'ordre des dépendances
        await prisma.$transaction([
          // 1. Supprimer d'abord les documents
          prisma.projectDocument.deleteMany({
            where: {
              OR: [{ folderId: id }, { folder: { parentId: id } }],
            },
          }),
          // 2. Supprimer ensuite les sous-dossiers
          prisma.folder.deleteMany({
            where: {
              OR: [
                { id }, // le dossier lui-même
                { parentId: id }, // ses enfants
              ],
            },
          }),
          // 3. Supprimer les fichiers
          prisma.file.deleteMany({
            where: {
              id: {
                in: allDocuments.map((doc) => doc.file?.id).filter(Boolean) as string[],
              },
            },
          }),
        ])

        return { success: true }
      }

      // Si ce n'est ni un projet ni un dossier, vérifier si c'est un fichier
      const fileDocument = await prisma.projectDocument.findUnique({
        where: { id },
        include: { file: true },
      })

      if (fileDocument) {
        // Supprimer physiquement le fichier de S3
        if (fileDocument.file) {
          const command = new DeleteObjectCommand({
            Bucket: env.NEXT_PUBLIC_S3_BUCKET_NAME,
            Key: fileDocument.file.key,
          })
          await s3Client?.send(command)
        }

        // Supprimer en respectant l'ordre des dépendances
        await prisma.$transaction([
          // 1. Supprimer d'abord le document
          prisma.projectDocument.delete({
            where: { id },
          }),
          // 2. Supprimer ensuite le fichier
          prisma.file.delete({
            where: { id: fileDocument.file?.id },
          }),
        ])

        return { success: true }
      }

      throw new TRPCError({ code: "NOT_FOUND", message: "Item not found" })
    } catch (error) {
      console.error("Delete error:", error)
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to delete item",
        cause: error instanceof Error ? error.message : String(error),
      })
    }
  }),
})

// Fonction utilitaire pour convertir la structure Prisma en FolderType
function convertToFolderType(projects: ProjectType[]): FolderType[] {
  const foldersMap = new Map<string, FolderType>()

  projects.forEach((project) => {
    // Tous les documents du projet (directs + ceux des dossiers)
    const allDocuments: ProjectDocument[] = [
      ...(project.allProjectDocuments || []),
      ...project.rootFolders.flatMap((folder) =>
        folder.documents.concat(
          // @ts-expect-error ProjectDocument
          folder.children.flatMap((child: { documents: ProjectDocument[] }) => child.documents)
        )
      ),
    ]

    // Ajouter le projet comme dossier racine avec tous les champs
    foldersMap.set(project.id, {
      id: project.id,
      title: project.name,
      createdAt: project.createdAt,
      parentId: null,
      type: "folder",
      description: project.description || undefined,
      projectSummary: project.projectSummary || undefined, // Utilisez le summary de la base
      projectSummaryEmbedding: project?.projectSummaryEmbedding || undefined,
      nombrefichiers: allDocuments.length,
      texteextraitdossier: allDocuments
        .map((d) => d.extractedText)
        .filter(Boolean)
        .join("\n\n"),
      files: allDocuments.map((doc) => ({
        id: doc.id,
        name: doc.originalFileName,
        type: doc.mimeType || "file",
        summary: doc.fileSummary || "",
        textextraitfichier: doc.extractedText || "",
        url: "doc",
      })),
    })

    const processFolder = (parentId: string, folder: ProjectFolder) => {
      // Construire l'item dossier
      const folderItem: FolderType = {
        id: folder.id,
        title: folder.name,
        createdAt: folder.createdAt,
        parentId,
        type: "folder",
        nombrefichiers: folder.documents.length,
        texteextraitdossier: folder.documents
          .map((d: { extractedText: string | null }) => d.extractedText)
          .filter(Boolean)
          .join("\n\n"),
      }

      // Ajouter ou mettre à jour le dossier dans la Map
      foldersMap.set(folder.id, folderItem)

      // Ajouter les fichiers du dossier
      folder.documents.forEach(
        (doc: {
          id: string
          originalFileName: string
          createdAt: Date
          mimeType?: string
          extractedText?: string | null
          fileSummary?: string | null
          fileSummaryEmbedding?: number[] | null
          file?: { key?: string }
        }) => {
          foldersMap.set(doc.id, {
            id: doc.id,
            title: doc.originalFileName,
            createdAt: doc.createdAt,
            parentId: folder.id,
            type: "file",
            url: doc.file?.key || "",
            mimeType: doc.mimeType,
            textextraitfichier: doc.extractedText || "",
            summary: doc.fileSummary || "",
            fileSummaryEmbedding: doc.fileSummaryEmbedding || undefined,
          })
        }
      )

      // Traiter les sous-dossiers
      folder.children.forEach((child: unknown) => {
        processFolder(folder.id, child as ProjectFolder)
      })
    }

    // Traiter les dossiers racine du projet
    project.rootFolders.forEach((folder) => {
      processFolder(project.id, folder)
    })
  })

  return Array.from(foldersMap.values()).filter(
    (item) =>
      item.type !== "folder" ||
      item.parentId !== null ||
      (foldersMap.get(item.id)?.type === "folder" && projects.some((p) => p.id === item.id))
  )
}
