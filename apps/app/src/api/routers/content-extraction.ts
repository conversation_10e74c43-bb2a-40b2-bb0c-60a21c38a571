import { z } from "zod"

import { extractContentFromFile } from "@/lib/ai/content-extraction"
import { auth } from "@/lib/auth"
import { publicProcedure, router } from "@/lib/server/trpc"
import { logger } from "@buildismart/lib"
import { TRPCError } from "@trpc/server"

const FileExtractionSchema = z.object({
  name: z.string().min(1, "File name is required"),
  type: z.string().min(1, "File type is required"),
  size: z.number().positive("File size must be positive"),
  data: z.array(z.number()).min(1, "File data is required"),
})

const ProjectSummarySchema = z.object({
  projectSummary: z.string().min(1, "Project summary is required"),
})

export const contentExtractionRouter = router({
  /**
   * Extract content from a file (text, summary, metadata)
   */
  extractFromFile: publicProcedure
    .input(FileExtractionSchema)
    .output(
      z.object({
        success: z.boolean(),
        extractedText: z.string(),
        summary: z.string(),
        fileName: z.string(),
        fileType: z.string(),
        metadata: z.object({
          wordCount: z.number(),
          pageCount: z.number(),
          size: z.number(),
          lastModified: z.string(),
        }),
        error: z.string().optional(),
      })
    )
    .mutation(async ({ input }) => {
      const session = await auth()
      if (!session?.user?.id) {
        throw new TRPCError({ code: "UNAUTHORIZED" })
      }

      try {
        logger.log("Starting content extraction", {
          fileName: input.name,
          fileType: input.type,
          fileSize: input.size,
          userId: session.user.id,
        })

        // Convert array of numbers back to File object
        const fileData = new Uint8Array(input.data)
        const file = new File([fileData], input.name, { type: input.type })

        // Extract content using our unified service
        const extractedContent = await extractContentFromFile(file)

        logger.log("Content extraction completed", {
          fileName: input.name,
          extractedLength: extractedContent.extractedText.length,
          summaryLength: extractedContent.summary.length,
          wordCount: extractedContent.metadata.wordCount,
        })

        return {
          success: true,
          extractedText: extractedContent.extractedText,
          summary: extractedContent.summary,
          fileName: input.name,
          fileType: input.type,
          metadata: extractedContent.metadata,
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Unknown extraction error"
        logger.error("Content extraction failed", {
          fileName: input.name,
          error: errorMessage,
          userId: session.user.id,
        })

        return {
          success: false,
          extractedText: "",
          summary: "Extraction failed",
          fileName: input.name,
          fileType: input.type,
          metadata: {
            wordCount: 0,
            pageCount: 0,
            size: input.size,
            lastModified: new Date().toISOString(),
          },
          error: errorMessage,
        }
      }
    }),

  /**
   * Generate a project summary from combined content
   */
  generateProjectSummary: publicProcedure
    .input(ProjectSummarySchema)
    .output(
      z.object({
        success: z.boolean(),
        summary: z.string(),
        error: z.string().optional(),
      })
    )
    .mutation(async ({ input }) => {
      const session = await auth()
      if (!session?.user?.id) {
        throw new TRPCError({ code: "UNAUTHORIZED" })
      }

      try {
        logger.log("Generating project summary", {
          contentLength: input.projectSummary.length,
          userId: session.user.id,
        })

        // Import OpenAI here to avoid circular dependencies
        const { OpenAI } = await import("openai")
        const { env } = await import("@/lib/env")

        const openai = new OpenAI({
          apiKey: env.OPENAI_API_KEY,
        })

        const truncatedContent = input.projectSummary.substring(0, 15000)

        const chatResponse = await openai.chat.completions.create({
          model: "gpt-4-turbo",
          messages: [
            {
              role: "system",
              content: `Tu es un expert en analyse de projets de construction (BTP - Bâtiment et Travaux Publics).
              Génère un résumé de projet extrêmement concis en exactement 2-3 lignes:
              - Focus sur le type de projet et les éléments techniques principaux
              - Mentionne les contraintes importantes et les spécifications clés
              - Style télégraphique, maximum 300 caractères total
              - Adapté pour les appels d'offres de construction`,
            },
            {
              role: "user",
              content: `Résume ce projet de construction en 2-3 lignes strictes:\n\n${truncatedContent}`,
            },
          ],
          temperature: 0.3,
          max_tokens: 200,
        })

        let summary = chatResponse.choices[0]?.message?.content || "Résumé de projet indisponible"

        // Clean up the result
        summary = summary.replace(/\n+/g, "\n").trim()
        const lines = summary.split("\n")

        if (lines.length > 3) {
          summary = lines.slice(0, 3).join("\n")
        }

        logger.log("Project summary generated", {
          summaryLength: summary.length,
          userId: session.user.id,
        })

        return {
          success: true,
          summary,
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Unknown summary generation error"
        logger.error("Project summary generation failed", {
          error: errorMessage,
          userId: session.user.id,
        })

        return {
          success: false,
          summary: "Génération du résumé échouée",
          error: errorMessage,
        }
      }
    }),

  /**
   * Get supported file types for content extraction
   */
  getSupportedFileTypes: publicProcedure
    .output(
      z.object({
        supportedTypes: z.array(
          z.object({
            type: z.string(),
            extensions: z.array(z.string()),
            description: z.string(),
            extractionMethod: z.string(),
          })
        ),
      })
    )
    .query(() => {
      return {
        supportedTypes: [
          {
            type: "application/pdf",
            extensions: [".pdf"],
            description: "Documents PDF",
            extractionMethod: "pdf-ts",
          },
          {
            type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            extensions: [".docx"],
            description: "Documents Word (DOCX)",
            extractionMethod: "mammoth",
          },
          {
            type: "application/msword",
            extensions: [".doc"],
            description: "Documents Word (DOC)",
            extractionMethod: "mammoth",
          },
          {
            type: "image/*",
            extensions: [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff"],
            description: "Images (OCR)",
            extractionMethod: "tesseract.js",
          },
          {
            type: "text/plain",
            extensions: [".txt"],
            description: "Fichiers texte",
            extractionMethod: "direct",
          },
          {
            type: "application/vnd.ms-excel",
            extensions: [".xls", ".xlsx"],
            description: "Fichiers Excel",
            extractionMethod: "node-tika",
          },
          {
            type: "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            extensions: [".pptx"],
            description: "Présentations PowerPoint",
            extractionMethod: "node-tika",
          },
        ],
      }
    }),
})
