import { vi } from 'vitest'

// Mock environment variables
vi.mock('@/lib/env', () => ({
  env: {
    OPENAI_API_KEY: 'test-openai-key',
    DATABASE_URL: 'postgresql://test:test@localhost:5432/test',
    NEXTAUTH_SECRET: 'test-secret',
    NEXTAUTH_URL: 'http://localhost:3000',
    AWS_ACCESS_KEY_ID: 'test-access-key',
    AWS_SECRET_ACCESS_KEY: 'test-secret-key',
    AWS_REGION: 'us-east-1',
    AWS_S3_BUCKET: 'test-bucket'
  }
}))

// Mock Prisma client
vi.mock('@/lib/prisma', () => ({
  prisma: {
    project: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn()
    },
    folder: {
      findFirst: vi.fn(),
      findMany: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn()
    },
    file: {
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn()
    },
    projectDocument: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn()
    },
    documentChunk: {
      findMany: vi.fn(),
      create: vi.fn(),
      deleteMany: vi.fn()
    },
    user: {
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn()
    },
    $transaction: vi.fn(),
    $queryRaw: vi.fn(),
    $executeRaw: vi.fn()
  }
}))

// Mock authentication
vi.mock('@/lib/auth', () => ({
  auth: vi.fn().mockResolvedValue({
    user: {
      id: 'test-user-id',
      email: '<EMAIL>',
      name: 'Test User'
    }
  })
}))

// Mock S3 client
vi.mock('@/lib/s3', () => ({
  s3Client: {
    send: vi.fn().mockResolvedValue({
      $metadata: { httpStatusCode: 200 }
    })
  }
}))

// Mock logger
vi.mock('@buildismart/lib', () => ({
  logger: {
    log: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn()
  }
}))

// Mock OpenAI
vi.mock('openai', () => ({
  OpenAI: vi.fn().mockImplementation(() => ({
    chat: {
      completions: {
        create: vi.fn().mockResolvedValue({
          choices: [{
            message: {
              content: 'Test AI response for construction document analysis'
            }
          }]
        })
      }
    },
    embeddings: {
      create: vi.fn().mockResolvedValue({
        data: [{
          embedding: Array(1536).fill(0).map(() => Math.random())
        }]
      })
    }
  }))
}))

// Mock file processing libraries
vi.mock('pdf-ts', () => ({
  pdfToText: vi.fn().mockResolvedValue('Extracted PDF content for testing')
}))

vi.mock('mammoth', () => ({
  extractRawText: vi.fn().mockResolvedValue({
    value: 'Extracted Word document content for testing'
  })
}))

vi.mock('tesseract.js', () => ({
  createWorker: vi.fn().mockResolvedValue({
    load: vi.fn(),
    loadLanguage: vi.fn(),
    initialize: vi.fn(),
    recognize: vi.fn().mockResolvedValue({
      data: { text: 'OCR extracted text for testing' }
    }),
    terminate: vi.fn()
  })
}))

vi.mock('node-tika', () => ({
  extract: vi.fn().mockResolvedValue('Tika extracted content for testing')
}))

// Mock AI SDK
vi.mock('ai', () => ({
  embed: vi.fn().mockResolvedValue({
    embedding: Array(1536).fill(0).map(() => Math.random())
  }),
  embedMany: vi.fn().mockResolvedValue({
    embeddings: [
      Array(1536).fill(0).map(() => Math.random()),
      Array(1536).fill(0).map(() => Math.random())
    ]
  }),
  streamText: vi.fn().mockResolvedValue({
    toDataStreamResponse: vi.fn().mockReturnValue(new Response('Test AI stream response'))
  }),
  tool: vi.fn().mockImplementation((config) => config)
}))

// Mock @ai-sdk/openai
vi.mock('@ai-sdk/openai', () => ({
  openai: vi.fn().mockReturnValue({
    model: 'gpt-4o',
    provider: 'openai'
  })
}))

// Global test utilities
global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

// Mock File API for Node.js environment
global.File = class File {
  name: string
  type: string
  size: number
  lastModified: number
  private content: ArrayBuffer

  constructor(bits: BlobPart[], filename: string, options: FilePropertyBag = {}) {
    this.name = filename
    this.type = options.type || ''
    this.lastModified = options.lastModified || Date.now()
    
    // Convert bits to ArrayBuffer
    const uint8Arrays = bits.map(bit => {
      if (bit instanceof ArrayBuffer) return new Uint8Array(bit)
      if (bit instanceof Uint8Array) return bit
      if (typeof bit === 'string') return new TextEncoder().encode(bit)
      return new Uint8Array()
    })
    
    const totalLength = uint8Arrays.reduce((sum, arr) => sum + arr.length, 0)
    const combined = new Uint8Array(totalLength)
    let offset = 0
    
    for (const arr of uint8Arrays) {
      combined.set(arr, offset)
      offset += arr.length
    }
    
    this.content = combined.buffer
    this.size = combined.length
  }

  async arrayBuffer(): Promise<ArrayBuffer> {
    return this.content
  }

  async text(): Promise<string> {
    return new TextDecoder().decode(this.content)
  }
}

// Setup console mocking for cleaner test output
const originalConsole = { ...console }
beforeEach(() => {
  vi.spyOn(console, 'log').mockImplementation(() => {})
  vi.spyOn(console, 'warn').mockImplementation(() => {})
  vi.spyOn(console, 'error').mockImplementation(() => {})
})

afterEach(() => {
  console.log = originalConsole.log
  console.warn = originalConsole.warn
  console.error = originalConsole.error
})
