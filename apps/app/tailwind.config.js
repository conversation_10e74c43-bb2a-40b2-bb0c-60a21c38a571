import { nextui } from "@nextui-org/theme"

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
    "./app/**/*.{js,ts,jsx,tsx}",
    "./src/**/*.{js,ts,jsx,tsx}",
    "../../node_modules/@nextui-org/theme/dist/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: "class",
  plugins: [
    nextui({
      themes: {
        light: {
          colors: {
            primary: {
              DEFAULT: "#1359AD", // primary 500
              foreground: "#F9FAFB",
            },
            secondary: {
              DEFAULT: "#F4AFA6", // Secondary 500 (rose clair)
              foreground: "#1F2937",
            },
            "couleur-secondaire": "#C9DAF8", // Bleu clair
            "couleur-tertiaire": "#FFF5F4", // <PERSON><PERSON> rosé
            background: "#F9FAFB",
            foreground: "#1F2937",
            focus: "#60A5FA",
            content1: "#FFFFFF",
            content2: "#F3F4F6",
            gray50: "#FAFAFA",
            gray100: "#F5F5F5",
            gray200: "#E5E5E5",
            gray300: "#D4D4D4",
            gray400: "#A3A3A3",
            gray500: "#737373",
          },
        },
        dark: {
          colors: {
            primary: {
              DEFAULT: "#1359AD",
              foreground: "#D1D5DB",
            },
            secondary: {
              DEFAULT: "#F4AFA6", // Secondary 500
              foreground: "#D1D5DB",
            },
            "couleur-secondaire": "#9DB4D8",
            "couleur-tertiaire": "#FFF5F4",
            background: "#111827",
            foreground: "#D1D5DB",
            focus: "#60A5FA",
            content1: "#1F2937",
            content2: "#374151",
            content: "#FFFFFF",
            gray50: "#1A1A1A",
            gray100: "#1F1F1F",
            gray200: "#252525",
            gray300: "#2C2C2C",
            gray400: "#444444",
            gray500: "#5B5B5B",
          },
        },
      },
    }),
  ],
  theme: {
    extend: {
      fontFamily: {
        main: ["Poppins", "sans-serif"], // Ajoutez cette ligne
      },
      colors: {
        primary: "#1359AD",
        secondary: "#F4AFA6",
        "couleur-secondaire": "#C9DAF8",
        "couleur-tertiaire": "#FFF5F4",
      },
    },
  },
}
