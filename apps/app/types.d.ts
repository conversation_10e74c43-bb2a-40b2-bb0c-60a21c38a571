import type { Default<PERSON><PERSON> } from "next-auth"

declare module "next-auth" {
  interface Session {
    user: Default<PERSON><PERSON> & {
      id: string
      role: string
      uuid: string
      username?: string
      role: string
      hasPassword: boolean
      emailVerified: Date | null
    }
  }
}

declare module "next-auth/jwt/types" {
  interface JWT {
    uuid: string
    role: string
    hasPassword: boolean
  }
}
