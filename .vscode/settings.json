{"tailwindCSS.experimental.classRegex": [["cva(?:<[^>]*>)?(([^)]*))", "[\"'`]([^\"'`]*).*?[\"'`]", "(?:twMerge|twJoin)\\(([^\\);]*)[\\);]"]], "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "editor.formatOnSave": true, "files.associations": {"*.css": "tailwindcss"}, "[terraform]": {"editor.defaultFormatter": "hashicorp.terraform", "editor.formatOnSave": true}, "WillLuke.nextjs.hasPrompted": true, "postman.settings.dotenv-detection-notification-visibility": false}